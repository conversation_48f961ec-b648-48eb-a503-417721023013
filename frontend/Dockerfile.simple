FROM nginx:alpine

# Créer un dossier simple pour l'application
RUN mkdir -p /usr/share/nginx/html

# Copier les fichiers HTML, CSS et JS
COPY public/index.html /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf

# Créer un simple fichier JavaScript pour l'application
RUN echo 'document.addEventListener("DOMContentLoaded", function() { \
    const app = document.getElementById("root"); \
    app.innerHTML = "<h1>Todo App - Projet FinOps</h1><div class=\"container\"><p>Application en cours de développement...</p><div class=\"alert alert-info\">Backend connecté à: http://backend-service:8080</div></div>"; \
});' > /usr/share/nginx/html/app.js

# Créer un fichier CSS simple
RUN echo 'body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f8f9fa; } \
.container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); } \
h1 { color: #343a40; text-align: center; margin-bottom: 30px; } \
.alert { padding: 15px; margin: 20px 0; border-radius: 5px; } \
.alert-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }' > /usr/share/nginx/html/styles.css

# Mettre à jour le index.html pour inclure les fichiers
RUN sed -i 's|</head>|<link rel="stylesheet" href="styles.css"><script src="app.js"></script></head>|' /usr/share/nginx/html/index.html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"] 