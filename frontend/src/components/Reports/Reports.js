import React, { useState, useEffect } from 'react';
import FinOpsService from '../../services/FinOpsService';

const Reports = () => {
  const [reportData, setReportData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [downloadingCSV, setDownloadingCSV] = useState(false);

  useEffect(() => {
    fetchReportData();
  }, []);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      const data = await FinOpsService.getCostReport();
      setReportData(data);
    } catch (error) {
      setError('Failed to load report data');
      console.error('Report error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadCSV = async () => {
    try {
      setDownloadingCSV(true);
      await FinOpsService.downloadCostReport('csv');
    } catch (error) {
      setError('Failed to download CSV report');
      console.error('Download error:', error);
    } finally {
      setDownloadingCSV(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount || 0);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger" role="alert">
        <h4 className="alert-heading">Error!</h4>
        <p>{error}</p>
        <button className="btn btn-outline-danger" onClick={fetchReportData}>
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="container-fluid py-4">
      <div className="row mb-4">
        <div className="col">
          <h1 className="h3 mb-0 text-gray-800">📄 Cost Reports</h1>
          <p className="text-muted">Generate and download cost reports</p>
        </div>
        <div className="col-auto">
          <button
            className="btn btn-success"
            onClick={handleDownloadCSV}
            disabled={downloadingCSV}
          >
            {downloadingCSV ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                Downloading...
              </>
            ) : (
              <>
                <i className="fas fa-download me-1"></i>
                Download CSV
              </>
            )}
          </button>
        </div>
      </div>

      {/* Summary Section */}
      {reportData?.summary && (
        <div className="row mb-4">
          <div className="col-12">
            <div className="card shadow">
              <div className="card-header py-3">
                <h6 className="m-0 font-weight-bold text-primary">Cost Summary</h6>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-3">
                    <div className="text-center">
                      <h4 className="text-primary">{formatCurrency(reportData.summary.totalCost)}</h4>
                      <p className="text-muted mb-0">Total Cost</p>
                    </div>
                  </div>
                  <div className="col-md-3">
                    <div className="text-center">
                      <h4 className="text-success">{formatCurrency(reportData.summary.cpuCost)}</h4>
                      <p className="text-muted mb-0">CPU Cost</p>
                    </div>
                  </div>
                  <div className="col-md-3">
                    <div className="text-center">
                      <h4 className="text-info">{formatCurrency(reportData.summary.memoryCost)}</h4>
                      <p className="text-muted mb-0">Memory Cost</p>
                    </div>
                  </div>
                  <div className="col-md-3">
                    <div className="text-center">
                      <h4 className="text-warning">{formatCurrency(reportData.summary.storageCost)}</h4>
                      <p className="text-muted mb-0">Storage Cost</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Daily Breakdown */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card shadow">
            <div className="card-header py-3">
              <h6 className="m-0 font-weight-bold text-primary">Daily Cost Breakdown</h6>
            </div>
            <div className="card-body">
              {reportData?.dailyBreakdown && reportData.dailyBreakdown.length > 0 ? (
                <div className="table-responsive">
                  <table className="table table-striped">
                    <thead>
                      <tr>
                        <th>Date</th>
                        <th>Total Cost</th>
                        <th>CPU Cost</th>
                        <th>Memory Cost</th>
                        <th>Storage Cost</th>
                        <th>Network Cost</th>
                        <th>Avg CPU Utilization</th>
                        <th>Avg Memory Utilization</th>
                      </tr>
                    </thead>
                    <tbody>
                      {reportData.dailyBreakdown.map((day, index) => (
                        <tr key={index}>
                          <td>{day._id?.period}</td>
                          <td className="fw-bold">{formatCurrency(day.totalCost)}</td>
                          <td>{formatCurrency(day.cpuCost)}</td>
                          <td>{formatCurrency(day.memoryCost)}</td>
                          <td>{formatCurrency(day.storageCost)}</td>
                          <td>{formatCurrency(day.networkCost)}</td>
                          <td>{(day.avgCpuUtilization || 0).toFixed(1)}%</td>
                          <td>{(day.avgMemoryUtilization || 0).toFixed(1)}%</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-5">
                  <i className="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                  <p className="text-muted">No daily breakdown data available</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Team Breakdown */}
      {reportData?.teamBreakdown && reportData.teamBreakdown.length > 0 && (
        <div className="row mb-4">
          <div className="col-12">
            <div className="card shadow">
              <div className="card-header py-3">
                <h6 className="m-0 font-weight-bold text-primary">Team Cost Breakdown</h6>
              </div>
              <div className="card-body">
                <div className="table-responsive">
                  <table className="table table-striped">
                    <thead>
                      <tr>
                        <th>Team</th>
                        <th>Environment</th>
                        <th>Total Cost</th>
                        <th>Resources</th>
                        <th>CPU Cost</th>
                        <th>Memory Cost</th>
                        <th>Storage Cost</th>
                      </tr>
                    </thead>
                    <tbody>
                      {reportData.teamBreakdown.map((team, index) => (
                        <tr key={index}>
                          <td>
                            <span className="badge bg-primary">
                              {team._id?.team || 'Unknown'}
                            </span>
                          </td>
                          <td>
                            <span className="badge bg-secondary">
                              {team._id?.environment || 'Unknown'}
                            </span>
                          </td>
                          <td className="fw-bold">{formatCurrency(team.totalCost)}</td>
                          <td>{team.uniqueResourceCount || 0}</td>
                          <td>{formatCurrency(team.cpuCost)}</td>
                          <td>{formatCurrency(team.memoryCost)}</td>
                          <td>{formatCurrency(team.storageCost)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Top Resources */}
      {reportData?.topResources && reportData.topResources.length > 0 && (
        <div className="row">
          <div className="col-12">
            <div className="card shadow">
              <div className="card-header py-3">
                <h6 className="m-0 font-weight-bold text-primary">Top Resources by Cost</h6>
              </div>
              <div className="card-body">
                <div className="table-responsive">
                  <table className="table table-striped">
                    <thead>
                      <tr>
                        <th>Resource Name</th>
                        <th>Type</th>
                        <th>Namespace</th>
                        <th>Total Cost</th>
                        <th>CPU Utilization</th>
                        <th>Memory Utilization</th>
                        <th>Data Points</th>
                      </tr>
                    </thead>
                    <tbody>
                      {reportData.topResources.map((resource, index) => (
                        <tr key={index}>
                          <td>{resource._id?.resourceName || 'Unknown'}</td>
                          <td>
                            <span className="badge bg-info">
                              {resource._id?.resourceType || 'Unknown'}
                            </span>
                          </td>
                          <td>{resource._id?.namespace || 'Unknown'}</td>
                          <td className="fw-bold">{formatCurrency(resource.totalCost)}</td>
                          <td>{(resource.avgCpuUtilization || 0).toFixed(1)}%</td>
                          <td>{(resource.avgMemoryUtilization || 0).toFixed(1)}%</td>
                          <td>{resource.dataPoints || 0}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Reports;
