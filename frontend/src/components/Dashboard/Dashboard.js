import React, { useState, useEffect } from 'react';
import FinOpsService from '../../services/FinOpsService';

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [budgets, setBudgets] = useState([]);
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
    fetchBudgets();
    fetchAlerts();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const data = await FinOpsService.getDashboard();
      setDashboardData(data.data || data);
    } catch (error) {
      setError('Failed to load dashboard data');
      console.error('Dashboard error:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchBudgets = async () => {
    try {
      const response = await FinOpsService.getBudgets({ limit: 5 });
      setBudgets(response.data || []);
    } catch (error) {
      console.error('Budget fetch error:', error);
    }
  };

  const fetchAlerts = async () => {
    try {
      const response = await FinOpsService.getAlerts();
      setAlerts(response.data || []);
    } catch (error) {
      console.error('Alerts fetch error:', error);
    }
  };

  const formatCurrency = (amount, currency = 'EUR') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount || 0);
  };

  const getProgressPercentage = (spent, total) => {
    if (!total || total === 0) return 0;
    return Math.min((spent / total) * 100, 100);
  };

  const getProgressColor = (percentage) => {
    if (percentage >= 90) return 'bg-danger';
    if (percentage >= 75) return 'bg-warning';
    return 'bg-success';
  };

  const getBudgetStatusBadge = (budget) => {
    const percentage = getProgressPercentage(budget.currentSpend?.amount || 0, budget.amounts?.total || 1);
    if (percentage >= 100) return <span className="badge bg-danger">Exceeded</span>;
    if (percentage >= 90) return <span className="badge bg-warning">Critical</span>;
    if (percentage >= 75) return <span className="badge bg-warning">Warning</span>;
    return <span className="badge bg-success">Healthy</span>;
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger" role="alert">
        <h4 className="alert-heading">Error!</h4>
        <p>{error}</p>
        <button className="btn btn-outline-danger" onClick={fetchDashboardData}>
          Try Again
        </button>
      </div>
    );
  }

  const { summary, trends, teamBreakdown, topResources } = dashboardData || {};

  return (
    <div className="container-fluid py-4">
      <div className="row mb-4">
        <div className="col">
          <h1 className="h3 mb-0 text-gray-800">💰 FinOps Dashboard</h1>
          <p className="text-muted">Financial Operations Overview</p>
        </div>
        <div className="col-auto">
          <button
            className="btn btn-outline-primary me-2"
            onClick={fetchDashboardData}
            disabled={loading}
          >
            <i className="fas fa-sync-alt me-1"></i>
            Refresh
          </button>
        </div>
      </div>

      {/* Alerts Section */}
      {alerts.length > 0 && (
        <div className="row mb-4">
          <div className="col-12">
            <div className="alert alert-warning alert-dismissible fade show" role="alert">
              <h6 className="alert-heading">
                <i className="fas fa-exclamation-triangle me-2"></i>
                Budget Alerts ({alerts.length})
              </h6>
              <div className="row">
                {alerts.slice(0, 3).map((alert, index) => (
                  <div key={index} className="col-md-4 mb-2">
                    <div className="d-flex align-items-center">
                      <i className="fas fa-wallet text-warning me-2"></i>
                      <div>
                        <strong>{alert.budgetName}</strong>
                        <br />
                        <small>
                          {alert.utilizationPercentage}% used
                          ({formatCurrency(alert.currentSpend)} / {formatCurrency(alert.totalAmount)})
                        </small>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              {alerts.length > 3 && (
                <small className="text-muted">
                  And {alerts.length - 3} more alerts...
                </small>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Summary Cards */}
      <div className="row mb-4">
        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-primary shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-primary text-uppercase mb-1">
                    Total Monthly Cost
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">
                    {formatCurrency(summary?.totalCost)}
                  </div>
                </div>
                <div className="col-auto">
                  <i className="fas fa-dollar-sign fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-success shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-success text-uppercase mb-1">
                    CPU Cost
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">
                    {formatCurrency(summary?.cpuCost)}
                  </div>
                </div>
                <div className="col-auto">
                  <i className="fas fa-microchip fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-info shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-info text-uppercase mb-1">
                    Memory Cost
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">
                    {formatCurrency(summary?.memoryCost)}
                  </div>
                </div>
                <div className="col-auto">
                  <i className="fas fa-memory fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-warning shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-warning text-uppercase mb-1">
                    Storage Cost
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">
                    {formatCurrency(summary?.storageCost)}
                  </div>
                </div>
                <div className="col-auto">
                  <i className="fas fa-hdd fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="row mb-4">
        <div className="col-xl-8 col-lg-7">
          <div className="card shadow mb-4">
            <div className="card-header py-3 d-flex flex-row align-items-center justify-content-between">
              <h6 className="m-0 font-weight-bold text-primary">Cost Trends (Last 30 Days)</h6>
            </div>
            <div className="card-body">
              {trends && trends.length > 0 ? (
                <div className="table-responsive">
                  <table className="table table-sm">
                    <thead>
                      <tr>
                        <th>Date</th>
                        <th>Total Cost</th>
                        <th>CPU</th>
                        <th>Memory</th>
                        <th>Storage</th>
                      </tr>
                    </thead>
                    <tbody>
                      {trends.slice(0, 10).map((trend, index) => (
                        <tr key={index}>
                          <td>{trend._id?.period}</td>
                          <td>{formatCurrency(trend.totalCost)}</td>
                          <td>{formatCurrency(trend.cpuCost)}</td>
                          <td>{formatCurrency(trend.memoryCost)}</td>
                          <td>{formatCurrency(trend.storageCost)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-3">
                  <i className="fas fa-chart-line fa-2x text-muted mb-2"></i>
                  <p className="text-muted">No cost trend data available</p>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="col-xl-4 col-lg-5">
          <div className="card shadow mb-4">
            <div className="card-header py-3 d-flex flex-row align-items-center justify-content-between">
              <h6 className="m-0 font-weight-bold text-primary">Team Breakdown</h6>
            </div>
            <div className="card-body">
              {teamBreakdown && teamBreakdown.length > 0 ? (
                <div>
                  {teamBreakdown.slice(0, 5).map((team, index) => (
                    <div key={index} className="mb-3">
                      <div className="d-flex justify-content-between">
                        <span className="small font-weight-bold">
                          {team._id?.team || 'Unknown'} ({team._id?.environment})
                        </span>
                        <span className="small">{formatCurrency(team.totalCost)}</span>
                      </div>
                      <div className="progress" style={{ height: '6px' }}>
                        <div
                          className="progress-bar bg-primary"
                          role="progressbar"
                          style={{ width: `${Math.min((team.totalCost / (teamBreakdown[0]?.totalCost || 1)) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted">No team data available</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Top Resources and Budgets */}
      <div className="row">
        <div className="col-lg-6 mb-4">
          <div className="card shadow">
            <div className="card-header py-3">
              <h6 className="m-0 font-weight-bold text-primary">Top Resources by Cost</h6>
            </div>
            <div className="card-body">
              {topResources && topResources.length > 0 ? (
                <div className="table-responsive">
                  <table className="table table-sm">
                    <thead>
                      <tr>
                        <th>Resource</th>
                        <th>Type</th>
                        <th>Cost</th>
                      </tr>
                    </thead>
                    <tbody>
                      {topResources.slice(0, 5).map((resource, index) => (
                        <tr key={index}>
                          <td className="small">{resource._id?.resourceName || 'Unknown'}</td>
                          <td className="small">{resource._id?.resourceType}</td>
                          <td className="small">{formatCurrency(resource.totalCost)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-muted">No resource data available</p>
              )}
            </div>
          </div>
        </div>

        <div className="col-lg-6 mb-4">
          <div className="card shadow">
            <div className="card-header py-3 d-flex justify-content-between align-items-center">
              <h6 className="m-0 font-weight-bold text-primary">Active Budgets</h6>
              <a href="/budgets" className="btn btn-sm btn-outline-primary">
                <i className="fas fa-plus me-1"></i>
                Manage
              </a>
            </div>
            <div className="card-body">
              {budgets && budgets.length > 0 ? (
                <div>
                  {budgets.slice(0, 4).map((budget) => {
                    const percentage = getProgressPercentage(budget.currentSpend?.amount || 0, budget.amounts?.total || 1);
                    return (
                      <div key={budget._id} className="mb-3 p-2 border rounded">
                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <div>
                            <span className="fw-bold">{budget.name}</span>
                            {getBudgetStatusBadge(budget)}
                          </div>
                          <span className="small text-muted">
                            {formatCurrency(budget.amounts?.total, budget.amounts?.currency)}
                          </span>
                        </div>
                        <div className="progress mb-1" style={{ height: '8px' }}>
                          <div
                            className={`progress-bar ${getProgressColor(percentage)}`}
                            role="progressbar"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <div className="d-flex justify-content-between">
                          <small className="text-muted">
                            {percentage.toFixed(1)}% used
                          </small>
                          <small className="text-muted">
                            {formatCurrency(budget.currentSpend?.amount || 0, budget.amounts?.currency)} spent
                          </small>
                        </div>
                        {budget.scope?.filters?.teams?.[0] && (
                          <small className="text-muted d-block mt-1">
                            <i className="fas fa-users me-1"></i>
                            {budget.scope.filters.teams[0]}
                          </small>
                        )}
                      </div>
                    );
                  })}
                  {budgets.length > 4 && (
                    <div className="text-center">
                      <a href="/budgets" className="btn btn-sm btn-link">
                        View all {budgets.length} budgets
                      </a>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-3">
                  <i className="fas fa-wallet fa-2x text-muted mb-2"></i>
                  <p className="text-muted mb-2">No active budgets</p>
                  <a href="/budgets" className="btn btn-sm btn-primary">
                    <i className="fas fa-plus me-1"></i>
                    Create Budget
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
