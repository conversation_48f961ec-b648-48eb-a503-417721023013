import React from 'react';

const SearchTodo = ({ 
  searchTerm, 
  onSearchChange, 
  filter, 
  onFilterChange, 
  onClearCompleted,
  hasCompleted 
}) => {
  return (
    <div className="row mb-3">
      <div className="col-md-6">
        <div className="input-group">
          <span className="input-group-text">🔍</span>
          <input
            type="text"
            className="form-control"
            placeholder="Rechercher des todos..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
          />
          {searchTerm && (
            <button 
              className="btn btn-outline-secondary" 
              type="button"
              onClick={() => onSearchChange('')}
            >
              ✕
            </button>
          )}
        </div>
      </div>
      
      <div className="col-md-4">
        <select 
          className="form-select"
          value={filter}
          onChange={(e) => onFilterChange(e.target.value)}
        >
          <option value="all">Tous les todos</option>
          <option value="pending">En cours</option>
          <option value="completed">Terminés</option>
        </select>
      </div>
      
      <div className="col-md-2">
        {hasCompleted && (
          <button 
            className="btn btn-outline-danger btn-sm w-100"
            onClick={onClearCompleted}
            title="Supprimer tous les todos terminés"
          >
            Nettoyer
          </button>
        )}
      </div>
    </div>
  );
};

export default SearchTodo;