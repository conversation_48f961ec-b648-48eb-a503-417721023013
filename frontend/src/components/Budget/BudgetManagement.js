import React, { useState, useEffect } from 'react';
import FinOpsService from '../../services/FinOpsService';

const BudgetManagement = () => {
  const [budgets, setBudgets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingBudget, setEditingBudget] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [newBudget, setNewBudget] = useState({
    name: '',
    description: '',
    scope: {
      type: 'team',
      filters: {
        teams: [''],
        projects: [''],
        environments: []
      }
    },
    period: {
      type: 'monthly',
      startDate: '',
      endDate: '',
      recurring: false
    },
    amounts: {
      total: '',
      currency: 'EUR'
    },
    alerts: {
      enabled: true,
      thresholds: [
        { percentage: 75, type: 'warning' },
        { percentage: 90, type: 'critical' }
      ]
    }
  });

  useEffect(() => {
    fetchBudgets();
  }, []);

  // Clear messages after 5 seconds
  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(() => {
        setError(null);
        setSuccess(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, success]);

  const fetchBudgets = async () => {
    try {
      setLoading(true);
      const response = await FinOpsService.getBudgets();
      setBudgets(response.data || []);
    } catch (error) {
      setError('Failed to load budgets');
      console.error('Budget error:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setNewBudget({
      name: '',
      description: '',
      scope: {
        type: 'team',
        filters: {
          teams: [''],
          projects: [''],
          environments: []
        }
      },
      period: {
        type: 'monthly',
        startDate: '',
        endDate: '',
        recurring: false
      },
      amounts: {
        total: '',
        currency: 'EUR'
      },
      alerts: {
        enabled: true,
        thresholds: [
          { percentage: 75, type: 'warning' },
          { percentage: 90, type: 'critical' }
        ]
      }
    });
  };

  const handleCreateBudget = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    try {
      const budgetData = {
        ...newBudget,
        amounts: {
          ...newBudget.amounts,
          total: parseFloat(newBudget.amounts.total)
        },
        period: {
          ...newBudget.period,
          startDate: new Date(newBudget.period.startDate).toISOString(),
          endDate: new Date(newBudget.period.endDate).toISOString()
        }
      };

      await FinOpsService.createBudget(budgetData);
      setSuccess('Budget created successfully!');
      setShowCreateForm(false);
      resetForm();
      fetchBudgets();
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to create budget');
      console.error('Create budget error:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditBudget = (budget) => {
    setEditingBudget(budget);
    setNewBudget({
      name: budget.name,
      description: budget.description || '',
      scope: budget.scope,
      period: {
        ...budget.period,
        startDate: budget.period.startDate ? new Date(budget.period.startDate).toISOString().split('T')[0] : '',
        endDate: budget.period.endDate ? new Date(budget.period.endDate).toISOString().split('T')[0] : ''
      },
      amounts: budget.amounts,
      alerts: budget.alerts
    });
    setShowCreateForm(true);
  };

  const handleUpdateBudget = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    try {
      const budgetData = {
        ...newBudget,
        amounts: {
          ...newBudget.amounts,
          total: parseFloat(newBudget.amounts.total)
        },
        period: {
          ...newBudget.period,
          startDate: new Date(newBudget.period.startDate).toISOString(),
          endDate: new Date(newBudget.period.endDate).toISOString()
        }
      };

      await FinOpsService.updateBudget(editingBudget._id, budgetData);
      setSuccess('Budget updated successfully!');
      setShowCreateForm(false);
      setEditingBudget(null);
      resetForm();
      fetchBudgets();
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to update budget');
      console.error('Update budget error:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteBudget = async (budgetId) => {
    setSubmitting(true);
    setError(null);

    try {
      await FinOpsService.deleteBudget(budgetId);
      setSuccess('Budget deleted successfully!');
      setShowDeleteConfirm(null);
      fetchBudgets();
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to delete budget');
      console.error('Delete budget error:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const cancelEdit = () => {
    setEditingBudget(null);
    setShowCreateForm(false);
    resetForm();
  };

  const formatCurrency = (amount, currency = 'EUR') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount || 0);
  };

  const getProgressPercentage = (spent, total) => {
    if (!total || total === 0) return 0;
    return Math.min((spent / total) * 100, 100);
  };

  const getProgressColor = (percentage) => {
    if (percentage >= 90) return 'bg-danger';
    if (percentage >= 75) return 'bg-warning';
    return 'bg-success';
  };

  const getBudgetStatusBadge = (budget) => {
    const percentage = getProgressPercentage(budget.currentSpend?.amount || 0, budget.amounts?.total || 1);
    if (percentage >= 100) return <span className="badge bg-danger">Exceeded</span>;
    if (percentage >= 90) return <span className="badge bg-warning">Critical</span>;
    if (percentage >= 75) return <span className="badge bg-warning">Warning</span>;
    if (budget.status === 'active') return <span className="badge bg-success">Active</span>;
    return <span className="badge bg-secondary">{budget.status}</span>;
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDaysRemaining = (endDate) => {
    if (!endDate) return null;
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid py-4">
      <div className="row mb-4">
        <div className="col">
          <h1 className="h3 mb-0 text-gray-800">💰 Budget Management</h1>
          <p className="text-muted">Manage and monitor your budgets</p>
        </div>
        <div className="col-auto">
          <button
            className="btn btn-primary"
            onClick={() => setShowCreateForm(true)}
          >
            <i className="fas fa-plus me-1"></i>
            Create Budget
          </button>
        </div>
      </div>

      {error && (
        <div className="alert alert-danger alert-dismissible fade show" role="alert">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
          <button type="button" className="btn-close" onClick={() => setError(null)}></button>
        </div>
      )}

      {success && (
        <div className="alert alert-success alert-dismissible fade show" role="alert">
          <i className="fas fa-check-circle me-2"></i>
          {success}
          <button type="button" className="btn-close" onClick={() => setSuccess(null)}></button>
        </div>
      )}

      {showCreateForm && (
        <div className="row mb-4">
          <div className="col-12">
            <div className="card">
              <div className="card-header d-flex justify-content-between align-items-center">
                <h5 className="mb-0">
                  <i className="fas fa-plus-circle me-2"></i>
                  {editingBudget ? 'Edit Budget' : 'Create New Budget'}
                </h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={cancelEdit}
                ></button>
              </div>
              <div className="card-body">
                <form onSubmit={editingBudget ? handleUpdateBudget : handleCreateBudget}>
                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">
                          <i className="fas fa-tag me-1"></i>
                          Budget Name *
                        </label>
                        <input
                          type="text"
                          className="form-control"
                          value={newBudget.name}
                          onChange={(e) => setNewBudget({...newBudget, name: e.target.value})}
                          placeholder="Enter budget name"
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-3">
                      <div className="mb-3">
                        <label className="form-label">
                          <i className="fas fa-euro-sign me-1"></i>
                          Total Amount *
                        </label>
                        <input
                          type="number"
                          className="form-control"
                          value={newBudget.amounts.total}
                          onChange={(e) => setNewBudget({
                            ...newBudget,
                            amounts: {...newBudget.amounts, total: e.target.value}
                          })}
                          placeholder="0.00"
                          min="0"
                          step="0.01"
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-3">
                      <div className="mb-3">
                        <label className="form-label">Currency</label>
                        <select
                          className="form-select"
                          value={newBudget.amounts.currency}
                          onChange={(e) => setNewBudget({
                            ...newBudget,
                            amounts: {...newBudget.amounts, currency: e.target.value}
                          })}
                        >
                          <option value="EUR">EUR (€)</option>
                          <option value="USD">USD ($)</option>
                          <option value="GBP">GBP (£)</option>
                          <option value="JPY">JPY (¥)</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label">
                      <i className="fas fa-align-left me-1"></i>
                      Description
                    </label>
                    <textarea
                      className="form-control"
                      rows="3"
                      value={newBudget.description}
                      onChange={(e) => setNewBudget({...newBudget, description: e.target.value})}
                      placeholder="Enter budget description (optional)"
                    />
                  </div>

                  <div className="row">
                    <div className="col-md-4">
                      <div className="mb-3">
                        <label className="form-label">
                          <i className="fas fa-layer-group me-1"></i>
                          Scope Type *
                        </label>
                        <select
                          className="form-select"
                          value={newBudget.scope.type}
                          onChange={(e) => setNewBudget({
                            ...newBudget,
                            scope: {...newBudget.scope, type: e.target.value}
                          })}
                          required
                        >
                          <option value="team">Team</option>
                          <option value="project">Project</option>
                          <option value="global">Global</option>
                          <option value="costCenter">Cost Center</option>
                        </select>
                      </div>
                    </div>
                    <div className="col-md-4">
                      <div className="mb-3">
                        <label className="form-label">
                          <i className="fas fa-calendar me-1"></i>
                          Period Type *
                        </label>
                        <select
                          className="form-select"
                          value={newBudget.period.type}
                          onChange={(e) => setNewBudget({
                            ...newBudget,
                            period: {...newBudget.period, type: e.target.value}
                          })}
                          required
                        >
                          <option value="monthly">Monthly</option>
                          <option value="quarterly">Quarterly</option>
                          <option value="yearly">Yearly</option>
                          <option value="custom">Custom</option>
                        </select>
                      </div>
                    </div>
                    <div className="col-md-4">
                      <div className="mb-3">
                        <label className="form-label">
                          <i className="fas fa-redo me-1"></i>
                          Recurring
                        </label>
                        <div className="form-check mt-2">
                          <input
                            className="form-check-input"
                            type="checkbox"
                            checked={newBudget.period.recurring}
                            onChange={(e) => setNewBudget({
                              ...newBudget,
                              period: {...newBudget.period, recurring: e.target.checked}
                            })}
                          />
                          <label className="form-check-label">
                            Auto-renew budget
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">
                          <i className="fas fa-users me-1"></i>
                          Team/Department
                        </label>
                        <input
                          type="text"
                          className="form-control"
                          value={newBudget.scope.filters.teams[0] || ''}
                          onChange={(e) => setNewBudget({
                            ...newBudget,
                            scope: {
                              ...newBudget.scope,
                              filters: {...newBudget.scope.filters, teams: [e.target.value]}
                            }
                          })}
                          placeholder="Enter team or department name"
                        />
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">
                          <i className="fas fa-project-diagram me-1"></i>
                          Project
                        </label>
                        <input
                          type="text"
                          className="form-control"
                          value={newBudget.scope.filters.projects[0] || ''}
                          onChange={(e) => setNewBudget({
                            ...newBudget,
                            scope: {
                              ...newBudget.scope,
                              filters: {...newBudget.scope.filters, projects: [e.target.value]}
                            }
                          })}
                          placeholder="Enter project name"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">
                          <i className="fas fa-calendar-alt me-1"></i>
                          Start Date *
                        </label>
                        <input
                          type="date"
                          className="form-control"
                          value={newBudget.period.startDate}
                          onChange={(e) => setNewBudget({
                            ...newBudget,
                            period: {...newBudget.period, startDate: e.target.value}
                          })}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">
                          <i className="fas fa-calendar-check me-1"></i>
                          End Date *
                        </label>
                        <input
                          type="date"
                          className="form-control"
                          value={newBudget.period.endDate}
                          onChange={(e) => setNewBudget({
                            ...newBudget,
                            period: {...newBudget.period, endDate: e.target.value}
                          })}
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div className="mb-3">
                    <label className="form-label">
                      <i className="fas fa-bell me-1"></i>
                      Alert Settings
                    </label>
                    <div className="card bg-light">
                      <div className="card-body">
                        <div className="form-check mb-2">
                          <input
                            className="form-check-input"
                            type="checkbox"
                            checked={newBudget.alerts.enabled}
                            onChange={(e) => setNewBudget({
                              ...newBudget,
                              alerts: {...newBudget.alerts, enabled: e.target.checked}
                            })}
                          />
                          <label className="form-check-label">
                            Enable budget alerts
                          </label>
                        </div>
                        {newBudget.alerts.enabled && (
                          <div className="row">
                            <div className="col-md-6">
                              <small className="text-muted">Warning threshold: 75%</small>
                            </div>
                            <div className="col-md-6">
                              <small className="text-muted">Critical threshold: 90%</small>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="d-flex gap-2">
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={submitting}
                    >
                      {submitting ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                          {editingBudget ? 'Updating...' : 'Creating...'}
                        </>
                      ) : (
                        <>
                          <i className={`fas ${editingBudget ? 'fa-save' : 'fa-plus'} me-1`}></i>
                          {editingBudget ? 'Update Budget' : 'Create Budget'}
                        </>
                      )}
                    </button>
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={cancelEdit}
                      disabled={submitting}
                    >
                      <i className="fas fa-times me-1"></i>
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="row">
        <div className="col-12">
          <div className="card shadow">
            <div className="card-header py-3">
              <h6 className="m-0 font-weight-bold text-primary">Active Budgets</h6>
            </div>
            <div className="card-body">
              {budgets.length > 0 ? (
                <div className="row">
                  {budgets.map((budget) => {
                    const percentage = getProgressPercentage(budget.currentSpend?.amount || 0, budget.amounts?.total || 1);
                    const daysRemaining = getDaysRemaining(budget.period?.endDate);

                    return (
                      <div key={budget._id} className="col-md-6 col-lg-4 mb-4">
                        <div className="card h-100 shadow-sm">
                          <div className="card-header d-flex justify-content-between align-items-center">
                            <h6 className="mb-0 text-truncate">{budget.name}</h6>
                            <div className="dropdown">
                              <button
                                className="btn btn-sm btn-outline-secondary dropdown-toggle"
                                type="button"
                                data-bs-toggle="dropdown"
                              >
                                <i className="fas fa-ellipsis-v"></i>
                              </button>
                              <ul className="dropdown-menu">
                                <li>
                                  <button
                                    className="dropdown-item"
                                    onClick={() => handleEditBudget(budget)}
                                  >
                                    <i className="fas fa-edit me-2"></i>Edit
                                  </button>
                                </li>
                                <li><hr className="dropdown-divider" /></li>
                                <li>
                                  <button
                                    className="dropdown-item text-danger"
                                    onClick={() => setShowDeleteConfirm(budget._id)}
                                  >
                                    <i className="fas fa-trash me-2"></i>Delete
                                  </button>
                                </li>
                              </ul>
                            </div>
                          </div>
                          <div className="card-body">
                            {budget.description && (
                              <p className="card-text text-muted small mb-3">{budget.description}</p>
                            )}

                            <div className="mb-3">
                              <div className="d-flex justify-content-between mb-1">
                                <span className="small">Spent</span>
                                <span className="small fw-bold">
                                  {formatCurrency(budget.currentSpend?.amount || 0, budget.amounts?.currency)}
                                </span>
                              </div>
                              <div className="d-flex justify-content-between mb-2">
                                <span className="small">Budget</span>
                                <span className="small fw-bold">
                                  {formatCurrency(budget.amounts?.total || 0, budget.amounts?.currency)}
                                </span>
                              </div>
                              <div className="progress mb-1" style={{ height: '8px' }}>
                                <div
                                  className={`progress-bar ${getProgressColor(percentage)}`}
                                  role="progressbar"
                                  style={{ width: `${percentage}%` }}
                                ></div>
                              </div>
                              <div className="d-flex justify-content-between">
                                <small className="text-muted">
                                  {percentage.toFixed(1)}% used
                                </small>
                                <small className="text-muted">
                                  {formatCurrency((budget.amounts?.total || 0) - (budget.currentSpend?.amount || 0), budget.amounts?.currency)} remaining
                                </small>
                              </div>
                            </div>

                            <div className="row g-2 small text-muted">
                              <div className="col-6">
                                <i className="fas fa-calendar me-1"></i>
                                {budget.period?.type}
                              </div>
                              <div className="col-6">
                                <i className="fas fa-users me-1"></i>
                                {budget.scope?.filters?.teams?.[0] || 'Global'}
                              </div>
                              <div className="col-6">
                                <i className="fas fa-clock me-1"></i>
                                {daysRemaining !== null ? (
                                  daysRemaining > 0 ? `${daysRemaining} days left` : 'Expired'
                                ) : 'No end date'}
                              </div>
                              <div className="col-6">
                                {getBudgetStatusBadge(budget)}
                              </div>
                            </div>

                            {budget.period?.endDate && (
                              <div className="mt-2 small text-muted">
                                <i className="fas fa-calendar-check me-1"></i>
                                Ends: {formatDate(budget.period.endDate)}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-5">
                  <i className="fas fa-wallet fa-3x text-muted mb-3"></i>
                  <p className="text-muted">No budgets found. Create your first budget to get started.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="modal fade show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">
                  <i className="fas fa-exclamation-triangle text-warning me-2"></i>
                  Confirm Delete
                </h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowDeleteConfirm(null)}
                ></button>
              </div>
              <div className="modal-body">
                <p>Are you sure you want to delete this budget?</p>
                <div className="alert alert-warning">
                  <i className="fas fa-info-circle me-2"></i>
                  This action cannot be undone. The budget will be permanently removed.
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setShowDeleteConfirm(null)}
                  disabled={submitting}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-danger"
                  onClick={() => handleDeleteBudget(showDeleteConfirm)}
                  disabled={submitting}
                >
                  {submitting ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                      Deleting...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-trash me-1"></i>
                      Delete Budget
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BudgetManagement;
