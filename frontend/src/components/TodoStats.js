import React, { useState, useEffect } from 'react';
import TodoService from '../services/TodoService';

const TodoStats = ({ todos }) => {
  const [serverStats, setServerStats] = useState(null);
  const [loading, setLoading] = useState(false);

  const fetchServerStats = () => {
    setLoading(true);
    TodoService.getStats()
      .then(response => {
        setServerStats(response.data);
        setLoading(false);
      })
      .catch(error => {
        console.error('Error fetching stats:', error);
        setLoading(false);
      });
  };

  useEffect(() => {
    if (todos.length > 0) {
      fetchServerStats();
    }
  }, [todos.length]);

  const localStats = {
    total: todos.length,
    completed: todos.filter(todo => todo.completed).length,
    pending: todos.filter(todo => !todo.completed).length,
    completionRate: todos.length > 0 ? 
      ((todos.filter(todo => todo.completed).length / todos.length) * 100).toFixed(1) : 0
  };

  return (
    <div className="card">
      <div className="card-header">
        <h5 className="card-title mb-0">📊 Statistiques</h5>
      </div>
      <div className="card-body">
        <div className="row text-center">
          <div className="col-4">
            <div className="border rounded p-2 mb-2">
              <div className="h4 text-primary mb-0">{localStats.total}</div>
              <small className="text-muted">Total</small>
            </div>
          </div>
          <div className="col-4">
            <div className="border rounded p-2 mb-2">
              <div className="h4 text-success mb-0">{localStats.completed}</div>
              <small className="text-muted">Terminés</small>
            </div>
          </div>
          <div className="col-4">
            <div className="border rounded p-2 mb-2">
              <div className="h4 text-warning mb-0">{localStats.pending}</div>
              <small className="text-muted">En cours</small>
            </div>
          </div>
        </div>
        
        <div className="mt-3">
          <div className="d-flex justify-content-between align-items-center mb-1">
            <span>Progression</span>
            <span>{localStats.completionRate}%</span>
          </div>
          <div className="progress">
            <div 
              className="progress-bar" 
              role="progressbar" 
              style={{ width: `${localStats.completionRate}%` }}
            ></div>
          </div>
        </div>

        {loading && (
          <div className="text-center mt-3">
            <div className="spinner-border spinner-border-sm" role="status">
              <span className="visually-hidden">Chargement...</span>
            </div>
          </div>
        )}

        {serverStats && (
          <div className="mt-3">
            <button 
              className="btn btn-outline-secondary btn-sm w-100"
              onClick={fetchServerStats}
            >
              🔄 Actualiser
            </button>
            <div className="mt-2">
              <small className="text-muted">
                Longueur moyenne: {parseFloat(serverStats.avgTitleLength).toFixed(1)} caractères
              </small>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TodoStats;