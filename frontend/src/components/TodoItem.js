import React from 'react';

const TodoItem = ({ todo, onDelete, onToggleComplete }) => {
  return (
    <div className="list-group-item d-flex justify-content-between align-items-start">
      <div className="ms-2 me-auto">
        <div className="fw-bold">
          <span 
            className={todo.completed ? 'text-decoration-line-through text-muted' : ''}
          >
            {todo.title}
          </span>
        </div>
        {todo.description && (
          <p className="mb-1 text-muted small">{todo.description}</p>
        )}
        <small className="text-muted">
          Créé le: {new Date(todo.createdAt).toLocaleDateString('fr-FR')}
        </small>
      </div>
      <div>
        <button
          className={`btn btn-sm me-2 ${todo.completed ? 'btn-warning' : 'btn-success'}`}
          onClick={() => onToggleComplete(todo._id)}
        >
          {todo.completed ? 'Réouvrir' : 'Terminer'}
        </button>
        <button
          className="btn btn-sm btn-danger"
          onClick={() => onDelete(todo._id)}
        >
          Supprimer
        </button>
      </div>
    </div>
  );
};

export default TodoItem;
