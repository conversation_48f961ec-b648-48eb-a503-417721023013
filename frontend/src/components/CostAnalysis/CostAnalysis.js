import React, { useState, useEffect } from 'react';
import FinOpsService from '../../services/FinOpsService';

const CostAnalysis = () => {
  const [costData, setCostData] = useState(null);
  const [budgets, setBudgets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [filters, setFilters] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    groupBy: 'day',
    filterBy: '',
    filterValue: ''
  });
  const [showAddCostForm, setShowAddCostForm] = useState(false);
  const [newCost, setNewCost] = useState({
    budgetId: '',
    amount: '',
    description: '',
    category: 'infrastructure',
    date: new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    fetchCostAnalysis();
    fetchBudgets();
  }, [filters]);

  const fetchCostAnalysis = async () => {
    try {
      setLoading(true);
      const data = await FinOpsService.getCostAnalysis(filters);
      setCostData(data.data || data);
    } catch (error) {
      setError('Failed to load cost analysis data');
      console.error('Cost analysis error:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchBudgets = async () => {
    try {
      const response = await FinOpsService.getBudgets();
      setBudgets(response.data || []);
    } catch (error) {
      console.error('Budget fetch error:', error);
    }
  };

  const formatCurrency = (amount, currency = 'EUR') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount || 0);
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddCost = async (e) => {
    e.preventDefault();
    try {
      // This would be a new API endpoint for adding manual cost entries
      // For now, we'll simulate it
      setSuccess('Cost entry added successfully!');
      setShowAddCostForm(false);
      setNewCost({
        budgetId: '',
        amount: '',
        description: '',
        category: 'infrastructure',
        date: new Date().toISOString().split('T')[0]
      });
      fetchCostAnalysis();
    } catch (error) {
      setError('Failed to add cost entry');
      console.error('Add cost error:', error);
    }
  };

  const downloadReport = async (format = 'csv') => {
    try {
      await FinOpsService.downloadCostReport(format);
      setSuccess(`Cost report downloaded as ${format.toUpperCase()}`);
    } catch (error) {
      setError('Failed to download report');
      console.error('Download error:', error);
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger" role="alert">
        <h4 className="alert-heading">Error!</h4>
        <p>{error}</p>
        <button className="btn btn-outline-danger" onClick={fetchCostAnalysis}>
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="container-fluid py-4">
      <div className="row mb-4">
        <div className="col">
          <h1 className="h3 mb-0 text-gray-800">📊 Cost Analysis</h1>
          <p className="text-muted">Detailed cost breakdown and trends</p>
        </div>
        <div className="col-auto">
          <div className="btn-group me-2">
            <button
              className="btn btn-outline-success"
              onClick={() => downloadReport('csv')}
            >
              <i className="fas fa-download me-1"></i>
              Export CSV
            </button>
            <button
              className="btn btn-outline-info"
              onClick={() => downloadReport('json')}
            >
              <i className="fas fa-download me-1"></i>
              Export JSON
            </button>
          </div>
          <button
            className="btn btn-primary"
            onClick={() => setShowAddCostForm(true)}
          >
            <i className="fas fa-plus me-1"></i>
            Add Cost Entry
          </button>
        </div>
      </div>

      {/* Notifications */}
      {error && (
        <div className="alert alert-danger alert-dismissible fade show" role="alert">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
          <button type="button" className="btn-close" onClick={() => setError(null)}></button>
        </div>
      )}

      {success && (
        <div className="alert alert-success alert-dismissible fade show" role="alert">
          <i className="fas fa-check-circle me-2"></i>
          {success}
          <button type="button" className="btn-close" onClick={() => setSuccess(null)}></button>
        </div>
      )}

      {/* Filters */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <h6 className="mb-0">
                <i className="fas fa-filter me-2"></i>
                Filters & Options
              </h6>
            </div>
            <div className="card-body">
              <div className="row">
                <div className="col-md-3">
                  <label className="form-label">Start Date</label>
                  <input
                    type="date"
                    className="form-control"
                    value={filters.startDate}
                    onChange={(e) => handleFilterChange('startDate', e.target.value)}
                  />
                </div>
                <div className="col-md-3">
                  <label className="form-label">End Date</label>
                  <input
                    type="date"
                    className="form-control"
                    value={filters.endDate}
                    onChange={(e) => handleFilterChange('endDate', e.target.value)}
                  />
                </div>
                <div className="col-md-3">
                  <label className="form-label">Group By</label>
                  <select
                    className="form-select"
                    value={filters.groupBy}
                    onChange={(e) => handleFilterChange('groupBy', e.target.value)}
                  >
                    <option value="hour">Hour</option>
                    <option value="day">Day</option>
                    <option value="week">Week</option>
                    <option value="month">Month</option>
                  </select>
                </div>
                <div className="col-md-3">
                  <label className="form-label">Filter By</label>
                  <select
                    className="form-select"
                    value={filters.filterBy}
                    onChange={(e) => handleFilterChange('filterBy', e.target.value)}
                  >
                    <option value="">All</option>
                    <option value="team">Team</option>
                    <option value="project">Project</option>
                    <option value="namespace">Namespace</option>
                    <option value="resourceType">Resource Type</option>
                    <option value="environment">Environment</option>
                  </select>
                </div>
              </div>
              {filters.filterBy && (
                <div className="row mt-3">
                  <div className="col-md-6">
                    <label className="form-label">Filter Value</label>
                    <input
                      type="text"
                      className="form-control"
                      value={filters.filterValue}
                      onChange={(e) => handleFilterChange('filterValue', e.target.value)}
                      placeholder={`Enter ${filters.filterBy} name`}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Add Cost Form */}
      {showAddCostForm && (
        <div className="row mb-4">
          <div className="col-12">
            <div className="card">
              <div className="card-header d-flex justify-content-between align-items-center">
                <h6 className="mb-0">
                  <i className="fas fa-plus-circle me-2"></i>
                  Add Cost Entry
                </h6>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowAddCostForm(false)}
                ></button>
              </div>
              <div className="card-body">
                <form onSubmit={handleAddCost}>
                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Budget</label>
                        <select
                          className="form-select"
                          value={newCost.budgetId}
                          onChange={(e) => setNewCost({...newCost, budgetId: e.target.value})}
                          required
                        >
                          <option value="">Select a budget</option>
                          {budgets.map(budget => (
                            <option key={budget._id} value={budget._id}>
                              {budget.name} ({formatCurrency(budget.amounts?.total, budget.amounts?.currency)})
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div className="col-md-3">
                      <div className="mb-3">
                        <label className="form-label">Amount</label>
                        <input
                          type="number"
                          className="form-control"
                          value={newCost.amount}
                          onChange={(e) => setNewCost({...newCost, amount: e.target.value})}
                          placeholder="0.00"
                          min="0"
                          step="0.01"
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-3">
                      <div className="mb-3">
                        <label className="form-label">Date</label>
                        <input
                          type="date"
                          className="form-control"
                          value={newCost.date}
                          onChange={(e) => setNewCost({...newCost, date: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                  </div>
                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Category</label>
                        <select
                          className="form-select"
                          value={newCost.category}
                          onChange={(e) => setNewCost({...newCost, category: e.target.value})}
                        >
                          <option value="infrastructure">Infrastructure</option>
                          <option value="software">Software</option>
                          <option value="personnel">Personnel</option>
                          <option value="training">Training</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Description</label>
                        <input
                          type="text"
                          className="form-control"
                          value={newCost.description}
                          onChange={(e) => setNewCost({...newCost, description: e.target.value})}
                          placeholder="Enter cost description"
                          required
                        />
                      </div>
                    </div>
                  </div>
                  <div className="d-flex gap-2">
                    <button type="submit" className="btn btn-primary">
                      <i className="fas fa-save me-1"></i>
                      Add Cost Entry
                    </button>
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={() => setShowAddCostForm(false)}
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Cost Data Display */}
      <div className="row">
        <div className="col-12">
          <div className="card shadow">
            <div className="card-header py-3 d-flex justify-content-between align-items-center">
              <h6 className="m-0 font-weight-bold text-primary">
                Cost Breakdown ({filters.groupBy})
              </h6>
              <div className="text-muted small">
                {filters.startDate} to {filters.endDate}
              </div>
            </div>
            <div className="card-body">
              {costData?.costs && costData.costs.length > 0 ? (
                <div className="table-responsive">
                  <table className="table table-striped table-hover">
                    <thead className="table-dark">
                      <tr>
                        <th>Period</th>
                        <th>Resource Type</th>
                        <th>Total Cost</th>
                        <th>CPU Cost</th>
                        <th>Memory Cost</th>
                        <th>Storage Cost</th>
                        <th>Network Cost</th>
                        <th>Count</th>
                      </tr>
                    </thead>
                    <tbody>
                      {costData.costs.map((cost, index) => (
                        <tr key={index}>
                          <td className="fw-bold">{cost._id?.date}</td>
                          <td>
                            <span className="badge bg-secondary">
                              {cost._id?.resourceType || 'Unknown'}
                            </span>
                          </td>
                          <td className="fw-bold text-primary">{formatCurrency(cost.totalCost)}</td>
                          <td>{formatCurrency(cost.cpuCost)}</td>
                          <td>{formatCurrency(cost.memoryCost)}</td>
                          <td>{formatCurrency(cost.storageCost)}</td>
                          <td>{formatCurrency(cost.networkCost)}</td>
                          <td>
                            <span className="badge bg-info">{cost.count}</span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-5">
                  <i className="fas fa-chart-line fa-3x text-muted mb-3"></i>
                  <h5 className="text-muted">No cost data available</h5>
                  <p className="text-muted">Try adjusting your filters or date range</p>
                  <button
                    className="btn btn-primary"
                    onClick={() => setShowAddCostForm(true)}
                  >
                    <i className="fas fa-plus me-1"></i>
                    Add First Cost Entry
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Budget vs Cost Comparison */}
      {budgets.length > 0 && (
        <div className="row mt-4">
          <div className="col-12">
            <div className="card shadow">
              <div className="card-header py-3">
                <h6 className="m-0 font-weight-bold text-primary">Budget vs Actual Cost Comparison</h6>
              </div>
              <div className="card-body">
                <div className="row">
                  {budgets.slice(0, 6).map(budget => {
                    const percentage = budget.amounts?.total ?
                      ((budget.currentSpend?.amount || 0) / budget.amounts.total * 100) : 0;
                    const progressColor = percentage >= 90 ? 'bg-danger' :
                                        percentage >= 75 ? 'bg-warning' : 'bg-success';

                    return (
                      <div key={budget._id} className="col-md-6 col-lg-4 mb-3">
                        <div className="card border">
                          <div className="card-body">
                            <h6 className="card-title">{budget.name}</h6>
                            <div className="d-flex justify-content-between mb-2">
                              <span>Budget:</span>
                              <span className="fw-bold">
                                {formatCurrency(budget.amounts?.total, budget.amounts?.currency)}
                              </span>
                            </div>
                            <div className="d-flex justify-content-between mb-2">
                              <span>Spent:</span>
                              <span className="fw-bold">
                                {formatCurrency(budget.currentSpend?.amount || 0, budget.amounts?.currency)}
                              </span>
                            </div>
                            <div className="progress mb-2" style={{ height: '8px' }}>
                              <div
                                className={`progress-bar ${progressColor}`}
                                role="progressbar"
                                style={{ width: `${Math.min(percentage, 100)}%` }}
                              ></div>
                            </div>
                            <small className="text-muted">
                              {percentage.toFixed(1)}% used
                            </small>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CostAnalysis;
