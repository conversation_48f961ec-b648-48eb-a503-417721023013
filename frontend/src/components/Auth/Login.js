import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import AuthService from '../../services/AuthService';

const Login = ({ onLogin }) => {
  const [formData, setFormData] = useState({
    identifier: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    setError(''); // Clear error when user types
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await AuthService.login(formData.identifier, formData.password);
      localStorage.setItem('token', response.token);
      onLogin(response.user);
    } catch (error) {
      console.error('Login error:', error);
      setError(error.response?.data?.message || 'Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="row justify-content-center">
      <div className="col-md-6 col-lg-4">
        <div className="card shadow-lg mt-5">
          <div className="card-body p-5">
            <div className="text-center mb-4">
              <h2 className="text-primary">💰 FinOps</h2>
              <p className="text-muted">Financial Operations Management</p>
            </div>

            {error && (
              <div className="alert alert-danger" role="alert">
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="mb-3">
                <label htmlFor="identifier" className="form-label">
                  Email or Username
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="identifier"
                  name="identifier"
                  value={formData.identifier}
                  onChange={handleChange}
                  required
                  placeholder="Enter your email or username"
                />
              </div>

              <div className="mb-3">
                <label htmlFor="password" className="form-label">
                  Password
                </label>
                <input
                  type="password"
                  className="form-control"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  placeholder="Enter your password"
                />
              </div>

              <button
                type="submit"
                className="btn btn-primary w-100"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                    Signing in...
                  </>
                ) : (
                  'Sign In'
                )}
              </button>
            </form>

            <div className="text-center mt-3">
              <p className="text-muted">
                Don't have an account?{' '}
                <Link to="/register" className="text-primary text-decoration-none">
                  Sign up here
                </Link>
              </p>
            </div>

            <div className="mt-4 p-3 bg-light rounded">
              <h6 className="text-muted mb-2">Demo Credentials:</h6>
              <small className="text-muted">
                <strong>Admin:</strong> <EMAIL> / FinOpsAdmin123!<br />
                <strong>User:</strong> <EMAIL> / TestPass123!
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
