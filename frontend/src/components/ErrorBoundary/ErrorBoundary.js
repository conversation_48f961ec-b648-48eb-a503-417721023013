import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Generate unique error ID for tracking
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Update state with error details
    this.setState({
      error,
      errorInfo,
      errorId
    });

    // Send error to logging service (if available)
    this.logErrorToService(error, errorInfo, errorId);
  }

  logErrorToService = (error, errorInfo, errorId) => {
    try {
      // In a real application, you would send this to your logging service
      const errorData = {
        errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')).id : null,
      };

      // Example: Send to your API
      // fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorData)
      // });

      console.log('Error logged:', errorData);
    } catch (loggingError) {
      console.error('Failed to log error:', loggingError);
    }
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      // Default fallback UI
      return (
        <div className="error-boundary">
          <div className="container mt-5">
            <div className="row justify-content-center">
              <div className="col-md-8">
                <div className="card border-danger">
                  <div className="card-header bg-danger text-white">
                    <h4 className="mb-0">
                      <i className="fas fa-exclamation-triangle me-2"></i>
                      Une erreur s'est produite
                    </h4>
                  </div>
                  <div className="card-body">
                    <p className="card-text">
                      Désolé, quelque chose s'est mal passé. L'erreur a été signalée à notre équipe.
                    </p>
                    
                    {process.env.NODE_ENV === 'development' && (
                      <div className="mt-3">
                        <h6>Détails de l'erreur (mode développement):</h6>
                        <div className="alert alert-secondary">
                          <strong>Message:</strong> {this.state.error?.message}
                          <br />
                          <strong>ID d'erreur:</strong> {this.state.errorId}
                        </div>
                        
                        <details className="mt-2">
                          <summary className="btn btn-outline-secondary btn-sm">
                            Voir la stack trace
                          </summary>
                          <pre className="mt-2 p-2 bg-light border rounded" style={{ fontSize: '0.8rem' }}>
                            {this.state.error?.stack}
                          </pre>
                        </details>
                        
                        <details className="mt-2">
                          <summary className="btn btn-outline-secondary btn-sm">
                            Voir la stack des composants
                          </summary>
                          <pre className="mt-2 p-2 bg-light border rounded" style={{ fontSize: '0.8rem' }}>
                            {this.state.errorInfo?.componentStack}
                          </pre>
                        </details>
                      </div>
                    )}
                    
                    <div className="mt-4">
                      <button 
                        className="btn btn-primary me-2" 
                        onClick={this.handleRetry}
                      >
                        <i className="fas fa-redo me-1"></i>
                        Réessayer
                      </button>
                      <button 
                        className="btn btn-secondary me-2" 
                        onClick={this.handleReload}
                      >
                        <i className="fas fa-refresh me-1"></i>
                        Recharger la page
                      </button>
                      <button 
                        className="btn btn-outline-primary" 
                        onClick={() => window.history.back()}
                      >
                        <i className="fas fa-arrow-left me-1"></i>
                        Retour
                      </button>
                    </div>
                    
                    {this.state.errorId && (
                      <div className="mt-3">
                        <small className="text-muted">
                          ID d'erreur: {this.state.errorId}
                        </small>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for wrapping components with error boundary
export const withErrorBoundary = (Component, fallback) => {
  return function WrappedComponent(props) {
    return (
      <ErrorBoundary fallback={fallback}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
};

// Hook for error handling in functional components
export const useErrorHandler = () => {
  const [error, setError] = React.useState(null);

  const resetError = () => setError(null);

  const captureError = (error) => {
    console.error('Error captured by useErrorHandler:', error);
    setError(error);
  };

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { captureError, resetError };
};

// Async error boundary for handling promise rejections
export class AsyncErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('AsyncErrorBoundary caught an error:', error, errorInfo);
  }

  componentDidMount() {
    // Listen for unhandled promise rejections
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection);
  }

  componentWillUnmount() {
    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);
  }

  handleUnhandledRejection = (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    this.setState({ hasError: true, error: event.reason });
    event.preventDefault();
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="alert alert-danger">
          <h5>Erreur asynchrone détectée</h5>
          <p>Une erreur s'est produite lors d'une opération asynchrone.</p>
          <button 
            className="btn btn-outline-danger"
            onClick={() => this.setState({ hasError: false, error: null })}
          >
            Réessayer
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
