import React from 'react';
import TodoItem from './TodoItem';

const TodoList = ({ todos, onDelete, onToggleComplete }) => {
  if (todos.length === 0) {
    return (
      <div className="text-center mt-4">
        <p className="text-muted">Aucune tâche à afficher</p>
      </div>
    );
  }

  return (
    <div className="mt-4">
      <h3>Liste des tâches ({todos.length})</h3>
      <div className="list-group">
        {todos.map(todo => (
          <TodoItem
            key={todo._id}
            todo={todo}
            onDelete={onDelete}
            onToggleComplete={onToggleComplete}
          />
        ))}
      </div>
    </div>
  );
};

export default TodoList;
