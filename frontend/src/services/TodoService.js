import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

class TodoService {
  getAll() {
    return api.get('/todos');
  }

  get(id) {
    return api.get(`/todos/${id}`);
  }

  create(data) {
    return api.post('/todos', data);
  }

  update(id, data) {
    return api.put(`/todos/${id}`, data);
  }

  delete(id) {
    return api.delete(`/todos/${id}`);
  }

  deleteAll() {
    return api.delete('/todos');
  }

  findByTitle(title) {
    return api.get(`/todos?title=${title}`);
  }

  searchByTitle(title) {
    return api.get(`/todos/search/${encodeURIComponent(title)}`);
  }

  getCompleted() {
    return api.get('/todos/completed');
  }

  getStats() {
    return api.get('/todos/stats/summary');
  }
}

export default new TodoService();
