import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

class TodoService {
  getAll() {
    return api.get('/todos');
  }

  get(id) {
    return api.get(`/todos/${id}`);
  }

  create(data) {
    return api.post('/todos', data);
  }

  update(id, data) {
    return api.put(`/todos/${id}`, data);
  }

  delete(id) {
    return api.delete(`/todos/${id}`);
  }

  deleteAll() {
    return api.delete('/todos');
  }

  findByTitle(title) {
    return api.get(`/todos?title=${title}`);
  }

  searchByTitle(title) {
    return api.get(`/todos/search/${encodeURIComponent(title)}`);
  }

  getCompleted() {
    return api.get('/todos/completed');
  }

  getStats() {
    return api.get('/todos/stats/summary');
  }
}

export default new TodoService();
