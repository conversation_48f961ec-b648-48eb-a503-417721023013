{"name": "todo-backend", "version": "1.0.0", "description": "Backend API pour Todo App - Projet FinOps", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.1", "express-validator": "^7.0.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.0.3", "winston": "^3.9.0"}, "devDependencies": {"nodemon": "^3.1.10"}, "keywords": ["todo", "api", "finops", "kubernetes"], "author": "DevOps Team", "license": "MIT"}