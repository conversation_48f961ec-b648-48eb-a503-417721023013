const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
require('dotenv').config();

const app = express();

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Variables d'état pour les health checks
let dbConnected = false;
let serverReady = false;

// Database connection avec retry
const db = require('./app/config/db.config.js');

const connectWithRetry = () => {
  console.log('🔄 Tentative de connexion à MongoDB...');
  db.mongoose
    .connect(db.url, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
    })
    .then(() => {
      console.log('✅ Connecté à la base de données MongoDB');
      dbConnected = true;
      serverReady = true;
    })
    .catch(err => {
      console.error('❌ Erreur de connexion à la base de données:', err.message);
      dbConnected = false;
      console.log('🔄 Nouvelle tentative dans 5 secondes...');
      setTimeout(connectWithRetry, 5000);
    });
};

// Démarrer la connexion
connectWithRetry();

// Health checks améliorés
app.get('/health', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    database: dbConnected ? 'connected' : 'disconnected',
    ready: serverReady
  };

  if (!dbConnected || !serverReady) {
    return res.status(503).json({
      ...health,
      status: 'unhealthy',
      message: 'Service not ready'
    });
  }

  // Test de ping à la base de données
  try {
    await db.mongoose.connection.db.admin().ping();
    health.databasePing = 'success';
  } catch (error) {
    health.databasePing = 'failed';
    health.databaseError = error.message;
    return res.status(503).json({
      ...health,
      status: 'unhealthy'
    });
  }

  res.json(health);
});

// Readiness probe endpoint
app.get('/ready', (req, res) => {
  if (serverReady && dbConnected) {
    res.json({ status: 'ready', timestamp: new Date().toISOString() });
  } else {
    res.status(503).json({ 
      status: 'not ready', 
      serverReady,
      dbConnected,
      timestamp: new Date().toISOString() 
    });
  }
});

// Liveness probe endpoint
app.get('/live', (req, res) => {
  res.json({ 
    status: 'alive', 
    uptime: process.uptime(),
    timestamp: new Date().toISOString() 
  });
});

// Routes principales
app.get('/', (req, res) => {
  res.json({ 
    message: 'API Todo - Projet FinOps (Version Fixed)',
    version: '1.1.0',
    status: dbConnected ? 'operational' : 'database disconnected',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      ready: '/ready',
      live: '/live',
      api: '/api/todos'
    }
  });
});

// Routes API
require('./app/routes/todo.routes.js')(app);

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({
    message: 'Erreur interne du serveur',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Erreur interne'
  });
});

// Gestion propre de l'arrêt
process.on('SIGTERM', () => {
  console.log('📤 Arrêt du serveur...');
  serverReady = false;
  setTimeout(() => {
    process.exit(0);
  }, 5000);
});

process.on('SIGINT', () => {
  console.log('📤 Arrêt du serveur (SIGINT)...');
  serverReady = false;
  setTimeout(() => {
    process.exit(0);
  }, 5000);
});

// Port
const PORT = process.env.PORT || 8080;
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Serveur en écoute sur le port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🎯 Ready check: http://localhost:${PORT}/ready`);
  console.log(`💓 Live check: http://localhost:${PORT}/live`);
});

module.exports = app; 