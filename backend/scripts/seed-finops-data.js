const mongoose = require('mongoose');
const Resource = require('../app/models/resource.model.js');
const Cost = require('../app/models/cost.model.js');
const Budget = require('../app/models/budget.model.js');
const User = require('../app/models/user.model.js');

require('dotenv').config();

async function seedFinOpsData() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.DB_URL || 'mongodb://localhost:27017/todoapp');
    console.log('Connected to MongoDB');

    // Find or create a test user
    let testUser = await User.findOne({ email: '<EMAIL>' });
    if (!testUser) {
      console.log('Test user not found. Please create a user first.');
      return;
    }

    // Clear existing data
    await Resource.deleteMany({});
    await Cost.deleteMany({});
    await Budget.deleteMany({});
    console.log('Cleared existing FinOps data');

    // Create sample resources
    const resources = [
      {
        name: 'frontend-deployment',
        type: 'deployment',
        namespace: 'finops-app',
        cluster: 'minikube',
        specifications: {
          cpu: { requests: '100m', limits: '500m' },
          memory: { requests: '128Mi', limits: '512Mi' },
          replicas: 2
        },
        status: 'running',
        owner: {
          userId: testUser._id,
          team: 'frontend-team',
          project: 'finops-todo-app',
          environment: 'production'
        },
        costCenter: 'engineering',
        tags: [
          { key: 'app', value: 'todo-frontend' },
          { key: 'tier', value: 'frontend' }
        ]
      },
      {
        name: 'backend-deployment',
        type: 'deployment',
        namespace: 'finops-app',
        cluster: 'minikube',
        specifications: {
          cpu: { requests: '200m', limits: '1000m' },
          memory: { requests: '256Mi', limits: '1Gi' },
          replicas: 3
        },
        status: 'running',
        owner: {
          userId: testUser._id,
          team: 'backend-team',
          project: 'finops-todo-app',
          environment: 'production'
        },
        costCenter: 'engineering',
        tags: [
          { key: 'app', value: 'todo-backend' },
          { key: 'tier', value: 'backend' }
        ]
      },
      {
        name: 'mongodb-statefulset',
        type: 'statefulset',
        namespace: 'finops-app',
        cluster: 'minikube',
        specifications: {
          cpu: { requests: '500m', limits: '2000m' },
          memory: { requests: '1Gi', limits: '4Gi' },
          storage: { requests: '10Gi', class: 'standard' },
          replicas: 1
        },
        status: 'running',
        owner: {
          userId: testUser._id,
          team: 'data-team',
          project: 'finops-todo-app',
          environment: 'production'
        },
        costCenter: 'infrastructure',
        tags: [
          { key: 'app', value: 'mongodb' },
          { key: 'tier', value: 'database' }
        ]
      }
    ];

    const createdResources = await Resource.insertMany(resources);
    console.log(`Created ${createdResources.length} resources`);

    // Create sample cost data for the last 30 days
    const costs = [];
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Pricing rates (per hour)
    const pricing = {
      cpu: { rate: 0.05, currency: 'EUR' }, // €0.05 per CPU core per hour
      memory: { rate: 0.01, currency: 'EUR' }, // €0.01 per GB per hour
      storage: { rate: 0.0001, currency: 'EUR' }, // €0.0001 per GB per hour
      network: { rate: 0.001, currency: 'EUR' } // €0.001 per GB transferred
    };

    for (const resource of createdResources) {
      // Generate cost data for each day
      for (let d = new Date(thirtyDaysAgo); d <= now; d.setDate(d.getDate() + 1)) {
        const timestamp = new Date(d);
        const periodStart = new Date(timestamp);
        const periodEnd = new Date(timestamp.getTime() + 60 * 60 * 1000); // 1 hour period

        // Simulate realistic usage patterns
        const baseUtilization = resource.type === 'statefulset' ? 0.7 : 0.4;
        const randomFactor = 0.5 + Math.random() * 0.5; // 0.5 to 1.0
        const cpuUtilization = Math.min(95, baseUtilization * randomFactor * 100);
        const memoryUtilization = Math.min(90, (baseUtilization + 0.1) * randomFactor * 100);

        // Calculate resource usage
        const cpuRequests = parseFloat(resource.specifications.cpu.requests?.replace('m', '') || '100') / 1000;
        const memoryRequests = parseFloat(resource.specifications.memory.requests?.replace('Mi', '') || '128') / 1024;
        const storageRequests = resource.specifications.storage?.requests ?
          parseFloat(resource.specifications.storage.requests.replace('Gi', '')) : 0;

        const cpuUsage = cpuRequests * (cpuUtilization / 100);
        const memoryUsage = memoryRequests * (memoryUtilization / 100);
        const storageUsage = storageRequests * 0.8; // Assume 80% storage utilization
        const networkIngress = Math.random() * 1000; // Random network usage in MB
        const networkEgress = Math.random() * 500;

        // Calculate costs
        const cpuCost = cpuUsage * pricing.cpu.rate;
        const memoryCost = memoryUsage * pricing.memory.rate;
        const storageCost = storageUsage * pricing.storage.rate;
        const networkCost = (networkIngress + networkEgress) / 1024 * pricing.network.rate;

        const costEntry = {
          resourceId: resource._id,
          timestamp,
          period: {
            start: periodStart,
            end: periodEnd,
            duration: 60 // 60 minutes
          },
          metrics: {
            cpu: {
              usage: cpuUsage,
              requests: cpuRequests,
              limits: parseFloat(resource.specifications.cpu.limits?.replace('m', '') || '500') / 1000,
              utilization: cpuUtilization
            },
            memory: {
              usage: memoryUsage * 1024 * 1024 * 1024, // Convert to bytes
              requests: memoryRequests * 1024 * 1024 * 1024,
              limits: parseFloat(resource.specifications.memory.limits?.replace(/Mi|Gi/, '') || '512') *
                (resource.specifications.memory.limits?.includes('Gi') ? 1024 * 1024 * 1024 : 1024 * 1024),
              utilization: memoryUtilization
            },
            storage: {
              usage: storageUsage * 1024 * 1024 * 1024, // Convert to bytes
              requests: storageRequests * 1024 * 1024 * 1024,
              utilization: storageRequests > 0 ? 80 : 0
            },
            network: {
              ingress: networkIngress * 1024 * 1024, // Convert to bytes
              egress: networkEgress * 1024 * 1024
            }
          },
          costs: {
            cpu: cpuCost,
            memory: memoryCost,
            storage: storageCost,
            network: networkCost,
            total: cpuCost + memoryCost + storageCost + networkCost
          },
          pricing,
          metadata: {
            cluster: resource.cluster,
            namespace: resource.namespace,
            resourceType: resource.type,
            resourceName: resource.name,
            team: resource.owner.team,
            project: resource.owner.project,
            environment: resource.owner.environment,
            costCenter: resource.costCenter
          }
        };

        costs.push(costEntry);
      }
    }

    await Cost.insertMany(costs);
    console.log(`Created ${costs.length} cost entries`);

    // Create sample budgets
    const budgets = [
      {
        name: 'Monthly Engineering Budget',
        description: 'Monthly budget for engineering team infrastructure',
        owner: {
          userId: testUser._id,
          team: 'engineering',
          project: 'finops-todo-app'
        },
        scope: {
          type: 'team',
          filters: {
            teams: ['frontend-team', 'backend-team', 'data-team'],
            environments: ['production']
          }
        },
        period: {
          type: 'monthly',
          startDate: new Date(now.getFullYear(), now.getMonth(), 1),
          endDate: new Date(now.getFullYear(), now.getMonth() + 1, 0),
          recurring: true
        },
        amounts: {
          total: 500,
          currency: 'EUR',
          breakdown: {
            cpu: 200,
            memory: 150,
            storage: 100,
            network: 50
          }
        },
        alerts: {
          enabled: true,
          thresholds: [
            {
              percentage: 75,
              type: 'warning',
              notificationChannels: [
                {
                  type: 'email',
                  target: testUser.email,
                  enabled: true
                }
              ]
            },
            {
              percentage: 90,
              type: 'critical',
              notificationChannels: [
                {
                  type: 'email',
                  target: testUser.email,
                  enabled: true
                }
              ]
            }
          ],
          recipients: [
            {
              userId: testUser._id,
              email: testUser.email,
              role: 'owner'
            }
          ]
        }
      }
    ];

    const createdBudgets = await Budget.insertMany(budgets);
    console.log(`Created ${createdBudgets.length} budgets`);

    console.log('FinOps sample data seeded successfully!');
    
  } catch (error) {
    console.error('Error seeding FinOps data:', error);
  } finally {
    await mongoose.disconnect();
  }
}

// Run the seeding function
if (require.main === module) {
  seedFinOpsData();
}

module.exports = seedFinOpsData;
