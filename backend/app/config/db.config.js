const mongoose = require('mongoose');
const logger = require('./logger.config.js');

// MongoDB connection options with optimization
const mongoOptions = {
  useNewUrlParser: true,
  useUnifiedTopology: true,

  // Connection pool settings
  maxPoolSize: parseInt(process.env.DB_MAX_POOL_SIZE) || 10, // Maximum number of connections
  minPoolSize: parseInt(process.env.DB_MIN_POOL_SIZE) || 2,  // Minimum number of connections
  maxIdleTimeMS: parseInt(process.env.DB_MAX_IDLE_TIME) || 30000, // Close connections after 30 seconds of inactivity

  // Timeout settings
  serverSelectionTimeoutMS: parseInt(process.env.DB_SERVER_SELECTION_TIMEOUT) || 30000, // How long to try selecting a server
  socketTimeoutMS: parseInt(process.env.DB_SOCKET_TIMEOUT) || 45000, // How long a send or receive on a socket can take
  connectTimeoutMS: parseInt(process.env.DB_CONNECT_TIMEOUT) || 30000, // How long to wait for a connection to be established

  // Heartbeat settings
  heartbeatFrequencyMS: parseInt(process.env.DB_HEARTBEAT_FREQUENCY) || 10000, // How often to check the server
};

// Performance monitoring
mongoose.set('debug', process.env.NODE_ENV === 'development');

// Connection event handlers
mongoose.connection.on('connected', () => {
  logger.info('MongoDB connected successfully', {
    host: mongoose.connection.host,
    port: mongoose.connection.port,
    name: mongoose.connection.name,
    readyState: mongoose.connection.readyState
  });
});

mongoose.connection.on('error', (err) => {
  logger.error('MongoDB connection error:', err);
});

mongoose.connection.on('disconnected', () => {
  logger.warn('MongoDB disconnected');
});

mongoose.connection.on('reconnected', () => {
  logger.info('MongoDB reconnected');
});

// Graceful shutdown
process.on('SIGINT', async () => {
  try {
    await mongoose.connection.close();
    logger.info('MongoDB connection closed through app termination');
    process.exit(0);
  } catch (error) {
    logger.error('Error during MongoDB connection closure:', error);
    process.exit(1);
  }
});

// Query performance monitoring
mongoose.plugin((schema) => {
  schema.pre(/^find/, function() {
    this.start = Date.now();
  });

  schema.post(/^find/, function() {
    if (this.start) {
      const duration = Date.now() - this.start;
      if (duration > 1000) { // Log slow queries (>1s)
        logger.performance('Slow MongoDB query', duration, {
          collection: this.getQuery(),
          operation: this.op,
          options: this.getOptions()
        });
      }
    }
  });
});

module.exports = {
  url: process.env.DB_URL || 'mongodb://localhost:27017/todoapp',
  options: mongoOptions,
  mongoose
};
