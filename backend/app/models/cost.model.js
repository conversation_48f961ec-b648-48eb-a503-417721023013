const mongoose = require('mongoose');

const costSchema = new mongoose.Schema({
  resourceId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Resource',
    required: [true, 'Resource ID is required'],
    index: true
  },
  timestamp: {
    type: Date,
    required: [true, 'Timestamp is required'],
    index: true
  },
  period: {
    start: {
      type: Date,
      required: [true, 'Period start is required']
    },
    end: {
      type: Date,
      required: [true, 'Period end is required']
    },
    duration: {
      type: Number, // Duration in minutes
      required: [true, 'Duration is required']
    }
  },
  metrics: {
    cpu: {
      usage: { type: Number, default: 0 }, // CPU cores used
      requests: { type: Number, default: 0 }, // CPU cores requested
      limits: { type: Number, default: 0 }, // CPU cores limit
      utilization: { type: Number, default: 0 } // Percentage utilization
    },
    memory: {
      usage: { type: Number, default: 0 }, // Memory in bytes
      requests: { type: Number, default: 0 }, // Memory requested in bytes
      limits: { type: Number, default: 0 }, // Memory limit in bytes
      utilization: { type: Number, default: 0 } // Percentage utilization
    },
    storage: {
      usage: { type: Number, default: 0 }, // Storage in bytes
      requests: { type: Number, default: 0 }, // Storage requested in bytes
      utilization: { type: Number, default: 0 } // Percentage utilization
    },
    network: {
      ingress: { type: Number, default: 0 }, // Bytes received
      egress: { type: Number, default: 0 } // Bytes sent
    }
  },
  costs: {
    cpu: {
      type: Number,
      default: 0,
      min: 0
    },
    memory: {
      type: Number,
      default: 0,
      min: 0
    },
    storage: {
      type: Number,
      default: 0,
      min: 0
    },
    network: {
      type: Number,
      default: 0,
      min: 0
    },
    total: {
      type: Number,
      default: 0,
      min: 0,
      index: true
    }
  },
  pricing: {
    cpu: {
      rate: { type: Number, default: 0 }, // Cost per CPU core per hour
      currency: { type: String, default: 'EUR' }
    },
    memory: {
      rate: { type: Number, default: 0 }, // Cost per GB per hour
      currency: { type: String, default: 'EUR' }
    },
    storage: {
      rate: { type: Number, default: 0 }, // Cost per GB per hour
      currency: { type: String, default: 'EUR' }
    },
    network: {
      rate: { type: Number, default: 0 }, // Cost per GB transferred
      currency: { type: String, default: 'EUR' }
    }
  },
  metadata: {
    cluster: {
      type: String,
      required: [true, 'Cluster name is required'],
      index: true
    },
    namespace: {
      type: String,
      required: [true, 'Namespace is required'],
      index: true
    },
    resourceType: {
      type: String,
      required: [true, 'Resource type is required'],
      index: true
    },
    resourceName: {
      type: String,
      required: [true, 'Resource name is required']
    },
    team: {
      type: String,
      index: true
    },
    project: {
      type: String,
      index: true
    },
    environment: {
      type: String,
      enum: ['development', 'staging', 'production', 'testing'],
      index: true
    },
    costCenter: {
      type: String,
      index: true
    }
  },
  tags: {
    type: Map,
    of: String,
    default: new Map()
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  }
});

// Compound indexes for performance
costSchema.index({ resourceId: 1, timestamp: -1 });
costSchema.index({ 'metadata.cluster': 1, 'metadata.namespace': 1, timestamp: -1 });
costSchema.index({ 'metadata.team': 1, 'metadata.environment': 1, timestamp: -1 });
costSchema.index({ 'metadata.project': 1, timestamp: -1 });
costSchema.index({ 'metadata.costCenter': 1, timestamp: -1 });
costSchema.index({ timestamp: -1, 'costs.total': -1 });

// Pre-save middleware to calculate total cost
costSchema.pre('save', function(next) {
  this.costs.total = this.costs.cpu + this.costs.memory + this.costs.storage + this.costs.network;
  next();
});

// Static methods for cost analysis
costSchema.statics.getCostsByDateRange = function(startDate, endDate, filters = {}) {
  const query = {
    timestamp: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    },
    ...filters
  };

  return this.aggregate([
    { $match: query },
    {
      $group: {
        _id: {
          date: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } },
          resourceType: '$metadata.resourceType'
        },
        totalCost: { $sum: '$costs.total' },
        cpuCost: { $sum: '$costs.cpu' },
        memoryCost: { $sum: '$costs.memory' },
        storageCost: { $sum: '$costs.storage' },
        networkCost: { $sum: '$costs.network' },
        count: { $sum: 1 }
      }
    },
    { $sort: { '_id.date': 1, '_id.resourceType': 1 } }
  ]);
};

costSchema.statics.getCostsByTeam = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        timestamp: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        },
        'metadata.team': { $exists: true, $ne: null }
      }
    },
    {
      $group: {
        _id: {
          team: '$metadata.team',
          environment: '$metadata.environment'
        },
        totalCost: { $sum: '$costs.total' },
        cpuCost: { $sum: '$costs.cpu' },
        memoryCost: { $sum: '$costs.memory' },
        storageCost: { $sum: '$costs.storage' },
        networkCost: { $sum: '$costs.network' },
        resourceCount: { $addToSet: '$resourceId' }
      }
    },
    {
      $addFields: {
        uniqueResourceCount: { $size: '$resourceCount' }
      }
    },
    {
      $project: {
        resourceCount: 0
      }
    },
    { $sort: { totalCost: -1 } }
  ]);
};

costSchema.statics.getTopCostResources = function(startDate, endDate, limit = 10) {
  return this.aggregate([
    {
      $match: {
        timestamp: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      }
    },
    {
      $group: {
        _id: {
          resourceId: '$resourceId',
          resourceName: '$metadata.resourceName',
          resourceType: '$metadata.resourceType',
          namespace: '$metadata.namespace'
        },
        totalCost: { $sum: '$costs.total' },
        avgCpuUtilization: { $avg: '$metrics.cpu.utilization' },
        avgMemoryUtilization: { $avg: '$metrics.memory.utilization' },
        dataPoints: { $sum: 1 }
      }
    },
    { $sort: { totalCost: -1 } },
    { $limit: limit }
  ]);
};

costSchema.statics.getCostTrends = function(startDate, endDate, granularity = 'day') {
  const dateFormat = granularity === 'hour' ? '%Y-%m-%d %H:00:00' : '%Y-%m-%d';
  
  return this.aggregate([
    {
      $match: {
        timestamp: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      }
    },
    {
      $group: {
        _id: {
          period: { $dateToString: { format: dateFormat, date: '$timestamp' } }
        },
        totalCost: { $sum: '$costs.total' },
        cpuCost: { $sum: '$costs.cpu' },
        memoryCost: { $sum: '$costs.memory' },
        storageCost: { $sum: '$costs.storage' },
        networkCost: { $sum: '$costs.network' },
        avgCpuUtilization: { $avg: '$metrics.cpu.utilization' },
        avgMemoryUtilization: { $avg: '$metrics.memory.utilization' }
      }
    },
    { $sort: { '_id.period': 1 } }
  ]);
};

module.exports = mongoose.model('Cost', costSchema);
