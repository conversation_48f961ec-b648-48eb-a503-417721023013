const mongoose = require('mongoose');

const budgetSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Budget name is required'],
    trim: true,
    maxlength: [100, 'Budget name cannot exceed 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  owner: {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Owner user ID is required'],
      index: true
    },
    team: {
      type: String,
      trim: true,
      index: true
    },
    project: {
      type: String,
      trim: true,
      index: true
    }
  },
  scope: {
    type: {
      type: String,
      enum: ['global', 'cluster', 'namespace', 'team', 'project', 'costCenter'],
      required: [true, 'Budget scope type is required'],
      index: true
    },
    filters: {
      clusters: [{ type: String }],
      namespaces: [{ type: String }],
      teams: [{ type: String }],
      projects: [{ type: String }],
      costCenters: [{ type: String }],
      resourceTypes: [{ type: String }],
      environments: [{
        type: String,
        enum: ['development', 'staging', 'production', 'testing']
      }],
      tags: {
        type: Map,
        of: String
      }
    }
  },
  period: {
    type: {
      type: String,
      enum: ['monthly', 'quarterly', 'yearly', 'custom'],
      required: [true, 'Budget period type is required']
    },
    startDate: {
      type: Date,
      required: [true, 'Budget start date is required'],
      index: true
    },
    endDate: {
      type: Date,
      required: [true, 'Budget end date is required'],
      index: true
    },
    recurring: {
      type: Boolean,
      default: false
    }
  },
  amounts: {
    total: {
      type: Number,
      required: [true, 'Total budget amount is required'],
      min: [0, 'Budget amount cannot be negative']
    },
    currency: {
      type: String,
      default: 'EUR',
      enum: ['EUR', 'USD', 'GBP', 'JPY']
    },
    breakdown: {
      cpu: { type: Number, default: 0, min: 0 },
      memory: { type: Number, default: 0, min: 0 },
      storage: { type: Number, default: 0, min: 0 },
      network: { type: Number, default: 0, min: 0 }
    }
  },
  alerts: {
    enabled: {
      type: Boolean,
      default: true
    },
    thresholds: [{
      percentage: {
        type: Number,
        required: true,
        min: 1,
        max: 100
      },
      type: {
        type: String,
        enum: ['warning', 'critical'],
        required: true
      },
      notificationChannels: [{
        type: {
          type: String,
          enum: ['email', 'slack', 'webhook'],
          required: true
        },
        target: {
          type: String,
          required: true
        },
        enabled: {
          type: Boolean,
          default: true
        }
      }],
      triggered: {
        type: Boolean,
        default: false
      },
      lastTriggered: {
        type: Date
      }
    }],
    recipients: [{
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      email: {
        type: String
      },
      role: {
        type: String,
        enum: ['owner', 'viewer', 'admin']
      }
    }]
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'expired', 'draft'],
    default: 'active',
    index: true
  },
  currentSpend: {
    amount: {
      type: Number,
      default: 0,
      min: 0
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    },
    breakdown: {
      cpu: { type: Number, default: 0 },
      memory: { type: Number, default: 0 },
      storage: { type: Number, default: 0 },
      network: { type: Number, default: 0 }
    }
  },
  forecasting: {
    enabled: {
      type: Boolean,
      default: true
    },
    projectedSpend: {
      type: Number,
      default: 0
    },
    projectionDate: {
      type: Date
    },
    confidence: {
      type: Number,
      min: 0,
      max: 100
    }
  },
  tags: {
    type: Map,
    of: String,
    default: new Map()
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for performance
budgetSchema.index({ 'owner.userId': 1, status: 1 });
budgetSchema.index({ 'owner.team': 1, 'period.startDate': 1 });
budgetSchema.index({ 'scope.type': 1, status: 1 });
budgetSchema.index({ 'period.endDate': 1, status: 1 });

// Virtual for budget utilization percentage
budgetSchema.virtual('utilizationPercentage').get(function() {
  if (this.amounts.total === 0) return 0;
  return Math.round((this.currentSpend.amount / this.amounts.total) * 100);
});

// Virtual for remaining budget
budgetSchema.virtual('remainingAmount').get(function() {
  return Math.max(0, this.amounts.total - this.currentSpend.amount);
});

// Virtual for budget status
budgetSchema.virtual('budgetStatus').get(function() {
  const utilization = this.utilizationPercentage;
  if (utilization >= 100) return 'exceeded';
  if (utilization >= 90) return 'critical';
  if (utilization >= 75) return 'warning';
  return 'healthy';
});

// Pre-save middleware
budgetSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Auto-expire budgets that have passed their end date
  if (this.period.endDate < new Date() && this.status === 'active') {
    this.status = 'expired';
  }
  
  next();
});

// Instance methods
budgetSchema.methods.updateCurrentSpend = async function(costData) {
  this.currentSpend.amount = costData.total || 0;
  this.currentSpend.breakdown = {
    cpu: costData.cpu || 0,
    memory: costData.memory || 0,
    storage: costData.storage || 0,
    network: costData.network || 0
  };
  this.currentSpend.lastUpdated = new Date();
  
  // Check alert thresholds
  await this.checkAlertThresholds();
  
  return this.save();
};

budgetSchema.methods.checkAlertThresholds = async function() {
  if (!this.alerts.enabled) return;
  
  const utilization = this.utilizationPercentage;
  
  for (const threshold of this.alerts.thresholds) {
    if (utilization >= threshold.percentage && !threshold.triggered) {
      threshold.triggered = true;
      threshold.lastTriggered = new Date();
      
      // Here you would trigger the actual alert notification
      // This could be implemented as a separate service
      console.log(`Budget alert triggered: ${this.name} at ${utilization}% utilization`);
    } else if (utilization < threshold.percentage && threshold.triggered) {
      // Reset threshold if utilization drops below
      threshold.triggered = false;
    }
  }
};

// Static methods
budgetSchema.statics.getActiveBudgets = function(userId = null) {
  const query = { status: 'active', isActive: true };
  if (userId) {
    query['owner.userId'] = userId;
  }
  return this.find(query).populate('owner.userId', 'username firstName lastName');
};

budgetSchema.statics.getBudgetsByTeam = function(team) {
  return this.find({
    'owner.team': team,
    status: 'active',
    isActive: true
  }).populate('owner.userId', 'username firstName lastName');
};

budgetSchema.statics.getExpiringBudgets = function(daysAhead = 30) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + daysAhead);
  
  return this.find({
    status: 'active',
    'period.endDate': { $lte: futureDate, $gte: new Date() }
  }).populate('owner.userId', 'username firstName lastName');
};

budgetSchema.statics.getBudgetStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalBudget: { $sum: '$amounts.total' },
        totalSpend: { $sum: '$currentSpend.amount' }
      }
    }
  ]);
};

module.exports = mongoose.model('Budget', budgetSchema);
