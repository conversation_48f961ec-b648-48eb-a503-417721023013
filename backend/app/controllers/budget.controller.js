const Budget = require('../models/budget.model.js');
const Cost = require('../models/cost.model.js');
const { validationResult } = require('express-validator');

// Create a new budget
exports.create = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const budgetData = {
      ...req.body,
      owner: {
        userId: req.user.userId,
        team: req.body.owner?.team || req.user.team,
        project: req.body.owner?.project
      }
    };

    const budget = new Budget(budgetData);
    await budget.save();

    res.status(201).json({
      success: true,
      message: 'Budget created successfully',
      data: budget
    });

  } catch (error) {
    console.error('Budget creation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create budget',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Get all budgets
exports.findAll = async (req, res) => {
  try {
    const { status, team, project, page = 1, limit = 10 } = req.query;
    
    // Build query based on user role
    let query = { isActive: true };
    
    if (req.user.role !== 'admin') {
      // Non-admin users can only see their own budgets or team budgets
      query.$or = [
        { 'owner.userId': req.user.userId },
        { 'owner.team': req.user.team }
      ];
    }
    
    if (status) query.status = status;
    if (team) query['owner.team'] = team;
    if (project) query['owner.project'] = project;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const [budgets, total] = await Promise.all([
      Budget.find(query)
        .populate('owner.userId', 'username firstName lastName')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      Budget.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: budgets,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Budget fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch budgets',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Get budget by ID
exports.findOne = async (req, res) => {
  try {
    const { id } = req.params;
    
    let query = { _id: id, isActive: true };
    
    // Non-admin users can only access their own budgets or team budgets
    if (req.user.role !== 'admin') {
      query.$or = [
        { 'owner.userId': req.user.userId },
        { 'owner.team': req.user.team }
      ];
    }

    const budget = await Budget.findOne(query)
      .populate('owner.userId', 'username firstName lastName');

    if (!budget) {
      return res.status(404).json({
        success: false,
        message: 'Budget not found or access denied'
      });
    }

    // Get current spending for this budget
    await updateBudgetSpending(budget);

    res.json({
      success: true,
      data: budget
    });

  } catch (error) {
    console.error('Budget fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch budget',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Update budget
exports.update = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    let query = { _id: id, isActive: true };
    
    // Non-admin users can only update their own budgets
    if (req.user.role !== 'admin') {
      query['owner.userId'] = req.user.userId;
    }

    // Prevent updating sensitive fields
    const allowedUpdates = [
      'name', 'description', 'amounts', 'alerts', 
      'period', 'scope', 'tags', 'status'
    ];
    
    const updateData = {};
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updateData[key] = req.body[key];
      }
    });

    const budget = await Budget.findOneAndUpdate(
      query,
      updateData,
      { new: true, runValidators: true }
    ).populate('owner.userId', 'username firstName lastName');

    if (!budget) {
      return res.status(404).json({
        success: false,
        message: 'Budget not found or access denied'
      });
    }

    res.json({
      success: true,
      message: 'Budget updated successfully',
      data: budget
    });

  } catch (error) {
    console.error('Budget update error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update budget',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Delete budget (soft delete)
exports.delete = async (req, res) => {
  try {
    const { id } = req.params;
    
    let query = { _id: id, isActive: true };
    
    // Non-admin users can only delete their own budgets
    if (req.user.role !== 'admin') {
      query['owner.userId'] = req.user.userId;
    }

    const budget = await Budget.findOneAndUpdate(
      query,
      { isActive: false, status: 'inactive' },
      { new: true }
    );

    if (!budget) {
      return res.status(404).json({
        success: false,
        message: 'Budget not found or access denied'
      });
    }

    res.json({
      success: true,
      message: 'Budget deleted successfully'
    });

  } catch (error) {
    console.error('Budget deletion error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete budget',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Get budget alerts
exports.getAlerts = async (req, res) => {
  try {
    let query = { 
      isActive: true, 
      status: 'active',
      'alerts.enabled': true
    };
    
    // Non-admin users can only see their own alerts
    if (req.user.role !== 'admin') {
      query.$or = [
        { 'owner.userId': req.user.userId },
        { 'owner.team': req.user.team }
      ];
    }

    const budgets = await Budget.find(query)
      .populate('owner.userId', 'username firstName lastName');

    // Filter budgets that have triggered alerts
    const alertedBudgets = budgets.filter(budget => {
      return budget.alerts.thresholds.some(threshold => threshold.triggered);
    });

    res.json({
      success: true,
      data: alertedBudgets.map(budget => ({
        budgetId: budget._id,
        budgetName: budget.name,
        owner: budget.owner,
        utilizationPercentage: budget.utilizationPercentage,
        currentSpend: budget.currentSpend.amount,
        totalAmount: budget.amounts.total,
        status: budget.budgetStatus,
        triggeredAlerts: budget.alerts.thresholds.filter(t => t.triggered)
      }))
    });

  } catch (error) {
    console.error('Budget alerts error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch budget alerts',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Update budget spending (helper function)
async function updateBudgetSpending(budget) {
  try {
    // Build cost query based on budget scope
    const costQuery = {
      timestamp: {
        $gte: budget.period.startDate,
        $lte: budget.period.endDate
      }
    };

    // Apply scope filters
    if (budget.scope.type !== 'global') {
      const filters = budget.scope.filters;
      
      if (filters.clusters?.length) {
        costQuery['metadata.cluster'] = { $in: filters.clusters };
      }
      if (filters.namespaces?.length) {
        costQuery['metadata.namespace'] = { $in: filters.namespaces };
      }
      if (filters.teams?.length) {
        costQuery['metadata.team'] = { $in: filters.teams };
      }
      if (filters.projects?.length) {
        costQuery['metadata.project'] = { $in: filters.projects };
      }
      if (filters.environments?.length) {
        costQuery['metadata.environment'] = { $in: filters.environments };
      }
    }

    // Get current spending
    const spending = await Cost.aggregate([
      { $match: costQuery },
      {
        $group: {
          _id: null,
          total: { $sum: '$costs.total' },
          cpu: { $sum: '$costs.cpu' },
          memory: { $sum: '$costs.memory' },
          storage: { $sum: '$costs.storage' },
          network: { $sum: '$costs.network' }
        }
      }
    ]);

    if (spending.length > 0) {
      await budget.updateCurrentSpend(spending[0]);
    }

  } catch (error) {
    console.error('Error updating budget spending:', error);
  }
}
