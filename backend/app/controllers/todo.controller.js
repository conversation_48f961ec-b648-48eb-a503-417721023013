const Todo = require('../models/todo.model.js');

// Créer et sauvegarder un nouveau todo
exports.create = (req, res) => {
  // Validation
  if (!req.body.title) {
    return res.status(400).json({
      success: false,
      message: 'Le titre ne peut pas être vide!'
    });
  }

  // Créer un todo avec l'ID de l'utilisateur
  const todo = new Todo({
    title: req.body.title,
    description: req.body.description || '',
    completed: req.body.completed || false,
    userId: req.user.userId // Add user ID from authenticated user
  });

  // Sauvegarder en base
  todo
    .save()
    .then(data => {
      res.json({
        success: true,
        message: 'Todo créé avec succès',
        data
      });
    })
    .catch(err => {
      console.error('Error creating todo:', err);
      res.status(500).json({
        success: false,
        message: err.message || 'Erreur lors de la création du todo.',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
      });
    });
};

// Récupérer tous les todos (user-specific or admin sees all)
exports.findAll = (req, res) => {
  const title = req.query.title;
  let condition = {};

  // If user is not admin, only show their todos
  if (req.user.role !== 'admin') {
    condition.userId = req.user.userId;
  }

  // Add title filter if provided
  if (title) {
    condition.title = { $regex: new RegExp(title), $options: 'i' };
  }

  Todo.find(condition)
    .sort({ createdAt: -1 })
    .populate('userId', 'username firstName lastName') // Populate user info
    .then(data => {
      res.json({
        success: true,
        count: data.length,
        data
      });
    })
    .catch(err => {
      console.error('Error fetching todos:', err);
      res.status(500).json({
        success: false,
        message: err.message || 'Erreur lors de la récupération des todos.',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
      });
    });
};

// Trouver un todo par ID
exports.findOne = (req, res) => {
  const id = req.params.id;
  const userId = req.user.userId;

  // Build secure query - user can only access their own todos unless admin
  let query = { _id: id };
  if (req.user.role !== 'admin') {
    query.userId = userId;
  }

  Todo.findOne(query)
    .populate('userId', 'username firstName lastName')
    .then(data => {
      if (!data) {
        return res.status(404).json({
          success: false,
          message: 'Todo non trouvé ou accès non autorisé'
        });
      }

      res.json({
        success: true,
        data
      });
    })
    .catch(err => {
      console.error('Error fetching todo:', err);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du todo',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
      });
    });
};

// Mettre à jour un todo par ID
exports.update = (req, res) => {
  if (!req.body) {
    return res.status(400).json({
      success: false,
      message: 'Les données à mettre à jour ne peuvent pas être vides!'
    });
  }

  const id = req.params.id;
  const userId = req.user.userId;

  // Build secure query - user can only update their own todos unless admin
  let query = { _id: id };
  if (req.user.role !== 'admin') {
    query.userId = userId;
  }

  // Sanitize update data - prevent updating sensitive fields
  const allowedUpdates = ['title', 'description', 'completed', 'priority', 'dueDate', 'tags'];
  const updateData = {};

  Object.keys(req.body).forEach(key => {
    if (allowedUpdates.includes(key)) {
      updateData[key] = req.body[key];
    }
  });

  Todo.findOneAndUpdate(query, updateData, { new: true, runValidators: true })
    .populate('userId', 'username firstName lastName')
    .then(data => {
      if (!data) {
        return res.status(404).json({
          success: false,
          message: 'Todo non trouvé ou accès non autorisé'
        });
      }

      res.json({
        success: true,
        message: 'Todo mis à jour avec succès',
        data
      });
    })
    .catch(err => {
      console.error('Error updating todo:', err);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du todo',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
      });
    });
};

// Supprimer un todo par ID
exports.delete = (req, res) => {
  const id = req.params.id;
  const userId = req.user.userId;

  // Build secure query - user can only delete their own todos unless admin
  let query = { _id: id };
  if (req.user.role !== 'admin') {
    query.userId = userId;
  }

  Todo.findOneAndDelete(query)
    .then(data => {
      if (!data) {
        return res.status(404).json({
          success: false,
          message: 'Todo non trouvé ou accès non autorisé'
        });
      }

      res.json({
        success: true,
        message: 'Todo supprimé avec succès!'
      });
    })
    .catch(err => {
      console.error('Error deleting todo:', err);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression du todo',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
      });
    });
};

// Supprimer tous les todos
exports.deleteAll = (req, res) => {
  Todo.deleteMany({})
    .then(data => {
      res.send({
        message: `${data.deletedCount} todos ont été supprimés avec succès!`
      });
    })
    .catch(err => {
      res.status(500).send({
        message: err.message || 'Erreur lors de la suppression des todos.'
      });
    });
};

// Trouver tous les todos complétés
exports.findAllCompleted = (req, res) => {
  Todo.find({ completed: true })
    .sort({ createdAt: -1 })
    .then(data => {
      res.send(data);
    })
    .catch(err => {
      res.status(500).send({
        message: err.message || 'Erreur lors de la récupération des todos complétés.'
      });
    });
};

// Rechercher des todos par titre
exports.findByTitle = (req, res) => {
  const title = req.params.title;
  const userId = req.user.userId;

  // Sanitize search term to prevent regex injection
  const sanitizedTitle = title.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

  // Build secure query
  let query = {
    title: { $regex: new RegExp(sanitizedTitle, 'i') }
  };

  // User can only search their own todos unless admin
  if (req.user.role !== 'admin') {
    query.userId = userId;
  }

  Todo.find(query)
    .sort({ createdAt: -1 })
    .populate('userId', 'username firstName lastName')
    .limit(50) // Limit results to prevent performance issues
    .then(data => {
      res.json({
        success: true,
        count: data.length,
        searchTerm: title,
        data
      });
    })
    .catch(err => {
      console.error('Error searching todos:', err);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la recherche des todos',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
      });
    });
};

// Obtenir des statistiques sur les todos
exports.getStats = (req, res) => {
  Promise.all([
    Todo.countDocuments(),
    Todo.countDocuments({ completed: true }),
    Todo.countDocuments({ completed: false }),
    Todo.aggregate([
      {
        $group: {
          _id: null,
          avgTitleLength: { $avg: { $strLenCP: '$title' } },
          oldestTodo: { $min: '$createdAt' },
          newestTodo: { $max: '$createdAt' }
        }
      }
    ])
  ])
  .then(([total, completed, pending, aggregates]) => {
    const stats = aggregates[0] || {};
    res.send({
      total,
      completed,
      pending,
      completionRate: total > 0 ? (completed / total * 100).toFixed(2) : 0,
      avgTitleLength: stats.avgTitleLength || 0,
      oldestTodo: stats.oldestTodo,
      newestTodo: stats.newestTodo
    });
  })
  .catch(err => {
    res.status(500).send({
      message: err.message || 'Erreur lors de la récupération des statistiques.'
    });
  });
};
