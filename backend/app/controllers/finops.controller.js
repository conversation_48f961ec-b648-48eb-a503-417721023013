const Cost = require('../models/cost.model.js');
const Resource = require('../models/resource.model.js');
const Budget = require('../models/budget.model.js');

// Get cost dashboard data
exports.getDashboard = async (req, res) => {
  try {
    const { startDate, endDate, team, project, environment } = req.query;
    
    // Default to last 30 days if no date range provided
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    // Build filters based on user role and query parameters
    const filters = {};
    if (req.user.role !== 'admin') {
      // Non-admin users can only see their team's data
      if (req.user.team) {
        filters['metadata.team'] = req.user.team;
      } else {
        // If user has no team, they can only see their own resources
        filters['metadata.userId'] = req.user.userId;
      }
    }
    
    if (team) filters['metadata.team'] = team;
    if (project) filters['metadata.project'] = project;
    if (environment) filters['metadata.environment'] = environment;

    // Get cost trends
    const costTrends = await Cost.getCostTrends(start, end, 'day');
    
    // Get costs by team
    const teamCosts = await Cost.getCostsByTeam(start, end);
    
    // Get top cost resources
    const topResources = await Cost.getTopCostResources(start, end, 10);
    
    // Get total costs for the period
    const totalCosts = await Cost.aggregate([
      {
        $match: {
          timestamp: { $gte: start, $lte: end },
          ...filters
        }
      },
      {
        $group: {
          _id: null,
          totalCost: { $sum: '$costs.total' },
          cpuCost: { $sum: '$costs.cpu' },
          memoryCost: { $sum: '$costs.memory' },
          storageCost: { $sum: '$costs.storage' },
          networkCost: { $sum: '$costs.network' },
          avgCpuUtilization: { $avg: '$metrics.cpu.utilization' },
          avgMemoryUtilization: { $avg: '$metrics.memory.utilization' }
        }
      }
    ]);

    // Get resource counts
    const resourceStats = await Resource.getResourceStats();
    
    // Get active budgets
    const budgets = await Budget.getActiveBudgets(
      req.user.role === 'admin' ? null : req.user.userId
    );

    res.json({
      success: true,
      data: {
        period: { start, end },
        summary: totalCosts[0] || {
          totalCost: 0,
          cpuCost: 0,
          memoryCost: 0,
          storageCost: 0,
          networkCost: 0,
          avgCpuUtilization: 0,
          avgMemoryUtilization: 0
        },
        trends: costTrends,
        teamBreakdown: teamCosts,
        topResources: topResources,
        resourceStats: resourceStats,
        budgets: budgets.map(budget => ({
          _id: budget._id,
          name: budget.name,
          totalAmount: budget.amounts.total,
          currentSpend: budget.currentSpend.amount,
          utilizationPercentage: budget.utilizationPercentage,
          status: budget.budgetStatus,
          endDate: budget.period.endDate
        }))
      }
    });

  } catch (error) {
    console.error('Dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to load dashboard data',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Get cost analysis
exports.getCostAnalysis = async (req, res) => {
  try {
    const { 
      startDate, 
      endDate, 
      groupBy = 'day',
      filterBy,
      filterValue 
    } = req.query;
    
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    // Build filters
    const filters = {
      timestamp: { $gte: start, $lte: end }
    };
    
    if (filterBy && filterValue) {
      filters[`metadata.${filterBy}`] = filterValue;
    }
    
    // Apply user role restrictions
    if (req.user.role !== 'admin' && req.user.team) {
      filters['metadata.team'] = req.user.team;
    }

    const costs = await Cost.getCostsByDateRange(start, end, filters);
    
    res.json({
      success: true,
      data: {
        period: { start, end },
        groupBy,
        filters: { filterBy, filterValue },
        costs
      }
    });

  } catch (error) {
    console.error('Cost analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get cost analysis',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Get resource utilization
exports.getResourceUtilization = async (req, res) => {
  try {
    const { startDate, endDate, resourceType, namespace } = req.query;
    
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const filters = {
      timestamp: { $gte: start, $lte: end }
    };
    
    if (resourceType) filters['metadata.resourceType'] = resourceType;
    if (namespace) filters['metadata.namespace'] = namespace;
    
    // Apply user role restrictions
    if (req.user.role !== 'admin' && req.user.team) {
      filters['metadata.team'] = req.user.team;
    }

    const utilization = await Cost.aggregate([
      { $match: filters },
      {
        $group: {
          _id: {
            resourceId: '$resourceId',
            resourceName: '$metadata.resourceName',
            resourceType: '$metadata.resourceType'
          },
          avgCpuUtilization: { $avg: '$metrics.cpu.utilization' },
          avgMemoryUtilization: { $avg: '$metrics.memory.utilization' },
          avgStorageUtilization: { $avg: '$metrics.storage.utilization' },
          maxCpuUtilization: { $max: '$metrics.cpu.utilization' },
          maxMemoryUtilization: { $max: '$metrics.memory.utilization' },
          totalCost: { $sum: '$costs.total' },
          dataPoints: { $sum: 1 }
        }
      },
      { $sort: { totalCost: -1 } }
    ]);

    res.json({
      success: true,
      data: {
        period: { start, end },
        utilization
      }
    });

  } catch (error) {
    console.error('Resource utilization error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get resource utilization',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Get cost optimization recommendations
exports.getOptimizationRecommendations = async (req, res) => {
  try {
    const { days = 7 } = req.query;
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);
    
    const filters = {
      timestamp: { $gte: startDate, $lte: endDate }
    };
    
    // Apply user role restrictions
    if (req.user.role !== 'admin' && req.user.team) {
      filters['metadata.team'] = req.user.team;
    }

    // Find underutilized resources
    const underutilized = await Cost.aggregate([
      { $match: filters },
      {
        $group: {
          _id: {
            resourceId: '$resourceId',
            resourceName: '$metadata.resourceName',
            resourceType: '$metadata.resourceType',
            namespace: '$metadata.namespace'
          },
          avgCpuUtilization: { $avg: '$metrics.cpu.utilization' },
          avgMemoryUtilization: { $avg: '$metrics.memory.utilization' },
          totalCost: { $sum: '$costs.total' },
          dataPoints: { $sum: 1 }
        }
      },
      {
        $match: {
          $or: [
            { avgCpuUtilization: { $lt: 20 } },
            { avgMemoryUtilization: { $lt: 20 } }
          ],
          dataPoints: { $gte: 10 } // Ensure we have enough data points
        }
      },
      { $sort: { totalCost: -1 } },
      { $limit: 20 }
    ]);

    // Find overutilized resources
    const overutilized = await Cost.aggregate([
      { $match: filters },
      {
        $group: {
          _id: {
            resourceId: '$resourceId',
            resourceName: '$metadata.resourceName',
            resourceType: '$metadata.resourceType',
            namespace: '$metadata.namespace'
          },
          avgCpuUtilization: { $avg: '$metrics.cpu.utilization' },
          avgMemoryUtilization: { $avg: '$metrics.memory.utilization' },
          maxCpuUtilization: { $max: '$metrics.cpu.utilization' },
          maxMemoryUtilization: { $max: '$metrics.memory.utilization' },
          totalCost: { $sum: '$costs.total' },
          dataPoints: { $sum: 1 }
        }
      },
      {
        $match: {
          $or: [
            { avgCpuUtilization: { $gt: 80 } },
            { avgMemoryUtilization: { $gt: 80 } }
          ],
          dataPoints: { $gte: 10 }
        }
      },
      { $sort: { totalCost: -1 } },
      { $limit: 20 }
    ]);

    // Generate recommendations
    const recommendations = [];
    
    underutilized.forEach(resource => {
      const potentialSavings = resource.totalCost * 0.3; // Estimate 30% savings
      recommendations.push({
        type: 'rightsizing',
        priority: 'medium',
        resource: resource._id,
        title: `Rightsize underutilized ${resource._id.resourceType}`,
        description: `${resource._id.resourceName} has low utilization (CPU: ${resource.avgCpuUtilization.toFixed(1)}%, Memory: ${resource.avgMemoryUtilization.toFixed(1)}%)`,
        potentialSavings: potentialSavings,
        action: 'Consider reducing resource requests or consolidating workloads'
      });
    });

    overutilized.forEach(resource => {
      recommendations.push({
        type: 'scaling',
        priority: 'high',
        resource: resource._id,
        title: `Scale up overutilized ${resource._id.resourceType}`,
        description: `${resource._id.resourceName} has high utilization (CPU: ${resource.avgCpuUtilization.toFixed(1)}%, Memory: ${resource.avgMemoryUtilization.toFixed(1)}%)`,
        potentialSavings: 0,
        action: 'Consider increasing resource limits or scaling horizontally'
      });
    });

    res.json({
      success: true,
      data: {
        period: { start: startDate, end: endDate },
        recommendations: recommendations.slice(0, 10), // Limit to top 10
        summary: {
          totalRecommendations: recommendations.length,
          potentialSavings: recommendations.reduce((sum, rec) => sum + rec.potentialSavings, 0),
          underutilizedResources: underutilized.length,
          overutilizedResources: overutilized.length
        }
      }
    });

  } catch (error) {
    console.error('Optimization recommendations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get optimization recommendations',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Generate cost report
exports.generateCostReport = async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      format = 'json',
      includeDetails = false,
      groupBy = 'team'
    } = req.query;

    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);

    const filters = {
      timestamp: { $gte: start, $lte: end }
    };

    // Apply user role restrictions
    if (req.user.role !== 'admin' && req.user.team) {
      filters['metadata.team'] = req.user.team;
    }

    // Get comprehensive cost data
    const [
      totalCosts,
      costTrends,
      teamCosts,
      topResources,
      resourceStats
    ] = await Promise.all([
      Cost.aggregate([
        { $match: filters },
        {
          $group: {
            _id: null,
            totalCost: { $sum: '$costs.total' },
            cpuCost: { $sum: '$costs.cpu' },
            memoryCost: { $sum: '$costs.memory' },
            storageCost: { $sum: '$costs.storage' },
            networkCost: { $sum: '$costs.network' }
          }
        }
      ]),
      Cost.getCostTrends(start, end, 'day'),
      Cost.getCostsByTeam(start, end),
      Cost.getTopCostResources(start, end, 20),
      Resource.getResourceStats()
    ]);

    const reportData = {
      metadata: {
        generatedAt: new Date(),
        generatedBy: req.user.username,
        period: { start, end },
        format,
        includeDetails
      },
      summary: totalCosts[0] || {
        totalCost: 0,
        cpuCost: 0,
        memoryCost: 0,
        storageCost: 0,
        networkCost: 0
      },
      trends: costTrends,
      breakdown: {
        byTeam: teamCosts,
        topResources: topResources
      },
      resourceStats: resourceStats
    };

    if (format === 'csv') {
      // Convert to CSV format
      const csv = convertToCSV(reportData);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename=cost-report-${start.toISOString().split('T')[0]}-to-${end.toISOString().split('T')[0]}.csv`);
      res.send(csv);
    } else {
      res.json({
        success: true,
        data: reportData
      });
    }

  } catch (error) {
    console.error('Cost report generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate cost report',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Helper function to convert data to CSV
function convertToCSV(data) {
  const lines = [];

  // Add summary
  lines.push('Cost Report Summary');
  lines.push(`Period,${data.metadata.period.start.toISOString().split('T')[0]} to ${data.metadata.period.end.toISOString().split('T')[0]}`);
  lines.push(`Total Cost,${data.summary.totalCost}`);
  lines.push(`CPU Cost,${data.summary.cpuCost}`);
  lines.push(`Memory Cost,${data.summary.memoryCost}`);
  lines.push(`Storage Cost,${data.summary.storageCost}`);
  lines.push(`Network Cost,${data.summary.networkCost}`);
  lines.push('');

  // Add team breakdown
  lines.push('Team Breakdown');
  lines.push('Team,Environment,Total Cost,CPU Cost,Memory Cost,Storage Cost,Network Cost');
  data.breakdown.byTeam.forEach(team => {
    lines.push(`${team._id.team},${team._id.environment},${team.totalCost},${team.cpuCost},${team.memoryCost},${team.storageCost},${team.networkCost}`);
  });

  return lines.join('\n');
}
