module.exports = app => {
  const todos = require('../controllers/todo.controller.js');
  const { todoValidation } = require('../middleware/validation.js');
  const { verifyToken, optionalAuth, requireAdmin } = require('../middleware/auth.middleware.js');

  const router = require('express').Router();

  // Public routes (no authentication required)
  // Health check and basic info can remain public

  // Protected routes (authentication required)
  // Créer un nouveau todo
  router.post('/', verifyToken, todoValidation.create, todos.create);

  // Récupérer tous les todos (user sees only their todos)
  router.get('/', verifyToken, todos.findAll);

  // Récupérer tous les todos complétés
  router.get('/completed', verifyToken, todos.findAllCompleted);

  // Récupérer un todo par id
  router.get('/:id', verifyToken, todoValidation.getId, todos.findOne);

  // Mettre à jour un todo par id
  router.put('/:id', verifyToken, todoValidation.update, todos.update);

  // Supprimer un todo par id
  router.delete('/:id', verifyToken, todoValidation.getId, todos.delete);

  // Recherche par titre
  router.get('/search/:title', verifyToken, todos.findByTitle);

  // Admin only routes
  // Supprimer tous les todos (admin only)
  router.delete('/', verifyToken, requireAdmin, todos.deleteAll);

  // Statistiques (admin only for global stats)
  router.get('/stats/summary', verifyToken, requireAdmin, todos.getStats);

  app.use('/api/todos', router);
};
