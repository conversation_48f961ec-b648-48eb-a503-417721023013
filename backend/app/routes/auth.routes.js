const express = require('express');
const rateLimit = require('express-rate-limit');
const authController = require('../controllers/auth.controller.js');
const { authValidation } = require('../middleware/validation.js');
const { verifyToken, authRateLimit } = require('../middleware/auth.middleware.js');

const router = express.Router();

// Rate limiting for authentication endpoints
const authLimiter = rateLimit(authRateLimit);

// Public routes (with rate limiting)
router.post('/register', authLimiter, authValidation.register, authController.register);
router.post('/login', authLimiter, authValidation.login, authController.login);
router.post('/refresh-token', authLimiter, authValidation.refreshToken, authController.refreshToken);

// Protected routes
router.get('/profile', verifyToken, authController.getProfile);
router.post('/logout', verifyToken, authController.logout);

module.exports = router;
