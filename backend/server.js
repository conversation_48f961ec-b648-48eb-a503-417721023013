const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const logger = require('./app/config/logger.config.js');
const { performanceMonitor, endpointPerformanceTracker, initializeMonitoring } = require('./app/middleware/performance.middleware.js');
require('dotenv').config();

const app = express();

// Security middleware - Helmet.js
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://stackpath.bootstrapcdn.com"],
      scriptSrc: ["'self'", "https://cdn.jsdelivr.net", "https://stackpath.bootstrapcdn.com"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for development
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// CORS configuration with support for file:// protocol and development
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps, curl requests, or file:// protocol)
    if (!origin) return callback(null, true);

    // Allow file:// protocol for local HTML files
    if (origin.startsWith('file://')) return callback(null, true);

    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:4000',
      'http://localhost:8080',
      'http://localhost:8081',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:3001',
      'http://127.0.0.1:4000',
      'http://127.0.0.1:8080',
      'http://127.0.0.1:8081',
      process.env.FRONTEND_URL,
      process.env.CORS_ORIGIN
    ].filter(Boolean);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      // In development mode, allow all origins
      if (process.env.NODE_ENV === 'development' || !process.env.NODE_ENV) {
        logger.warn(`CORS: Allowing origin in development mode: ${origin}`);
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin']
};

app.use(cors(corsOptions));

// Rate limiting
const generalLimiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(generalLimiter);

// Request logging middleware
app.use(logger.requestLogger);

// Performance monitoring middleware
app.use(performanceMonitor);
app.use(endpointPerformanceTracker());

// Body parsing middleware with size limits
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// Variables d'état pour les health checks
let dbConnected = false;
let serverReady = false;

// Database connection avec retry
const db = require('./app/config/db.config.js');

const connectWithRetry = () => {
  logger.info('🔄 Tentative de connexion à MongoDB...');
  db.mongoose
    .connect(db.url, db.options)
    .then(() => {
      logger.info('✅ Connecté à la base de données MongoDB');
      dbConnected = true;
      serverReady = true;
    })
    .catch(err => {
      logger.error('❌ Erreur de connexion à la base de données:', err);
      dbConnected = false;
      logger.info('🔄 Nouvelle tentative dans 5 secondes...');
      setTimeout(connectWithRetry, 5000);
    });
};

// Démarrer la connexion
connectWithRetry();

// Initialize performance monitoring
initializeMonitoring();

// Health checks améliorés
app.get('/health', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    database: dbConnected ? 'connected' : 'disconnected',
    ready: serverReady
  };

  if (!dbConnected || !serverReady) {
    return res.status(503).json({
      ...health,
      status: 'unhealthy',
      message: 'Service not ready'
    });
  }

  // Test de ping à la base de données
  try {
    await db.mongoose.connection.db.admin().ping();
    health.databasePing = 'success';
  } catch (error) {
    health.databasePing = 'failed';
    health.databaseError = error.message;
    return res.status(503).json({
      ...health,
      status: 'unhealthy'
    });
  }

  res.json(health);
});

// Readiness probe endpoint
app.get('/ready', (req, res) => {
  if (serverReady && dbConnected) {
    res.json({ status: 'ready', timestamp: new Date().toISOString() });
  } else {
    res.status(503).json({ 
      status: 'not ready', 
      serverReady,
      dbConnected,
      timestamp: new Date().toISOString() 
    });
  }
});

// Liveness probe endpoint
app.get('/live', (req, res) => {
  res.json({ 
    status: 'alive', 
    uptime: process.uptime(),
    timestamp: new Date().toISOString() 
  });
});

// Routes principales
app.get('/', (req, res) => {
  res.json({ 
    message: 'API Todo - Projet FinOps (Version Fixed)',
    version: '1.1.0',
    status: dbConnected ? 'operational' : 'database disconnected',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      ready: '/ready',
      live: '/live',
      api: '/api/todos'
    }
  });
});

// Routes API
require('./app/routes/todo.routes.js')(app);

// Authentication routes
const authRoutes = require('./app/routes/auth.routes.js');
app.use('/api/auth', authRoutes);

// FinOps routes
const finopsRoutes = require('./app/routes/finops.routes.js');
app.use('/api/finops', finopsRoutes);

// Error handling middleware (must be last)
const { globalErrorHandler, notFound } = require('./app/middleware/error.middleware.js');

// Handle 404 for undefined routes
app.all('*', notFound);

// Global error handler
app.use(globalErrorHandler);

// Gestion propre de l'arrêt
process.on('SIGTERM', () => {
  console.log('📤 Arrêt du serveur...');
  serverReady = false;
  setTimeout(() => {
    process.exit(0);
  }, 5000);
});

process.on('SIGINT', () => {
  console.log('📤 Arrêt du serveur (SIGINT)...');
  serverReady = false;
  setTimeout(() => {
    process.exit(0);
  }, 5000);
});

// Port
const PORT = process.env.PORT || 8080;
const server = app.listen(PORT, '0.0.0.0', () => {
  logger.info(`🚀 Serveur en écoute sur le port ${PORT}`);
  logger.info(`📊 Health check: http://localhost:${PORT}/health`);
  logger.info(`🎯 Ready check: http://localhost:${PORT}/ready`);
  logger.info(`💓 Live check: http://localhost:${PORT}/live`);
});

module.exports = app; 