apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-fixed
  namespace: finops-app
  labels:
    app: frontend
    version: fixed
spec:
  replicas: 1
  selector:
    matchLabels:
      app: frontend
      version: fixed
  template:
    metadata:
      labels:
        app: frontend
        version: fixed
    spec:
      containers:
      - name: frontend
        image: frontend:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 80
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 250m
            memory: 256Mi
        env:
        - name: REACT_APP_API_URL
          value: "http://backend-service-fixed:8080/api"
        livenessProbe:
          httpGet:
            path: /live
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /live
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 20
        volumeMounts:
        - name: nginx-config-fixed
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
      volumes:
      - name: nginx-config-fixed
        configMap:
          name: nginx-config-fixed 