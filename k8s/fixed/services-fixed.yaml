apiVersion: v1
kind: Service
metadata:
  name: mongo-service-fixed
  namespace: finops-app
  labels:
    app: mongo
    version: fixed
spec:
  selector:
    app: mongo
    version: fixed
  ports:
  - port: 27017
    targetPort: 27017
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: backend-service-fixed
  namespace: finops-app
  labels:
    app: backend
    version: fixed
spec:
  selector:
    app: backend
    version: fixed
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service-fixed
  namespace: finops-app
  labels:
    app: frontend
    version: fixed
spec:
  selector:
    app: frontend
    version: fixed
  ports:
  - port: 80
    targetPort: 80
    nodePort: 30082
  type: NodePort 