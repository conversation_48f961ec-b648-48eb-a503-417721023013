apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongo-fixed
  namespace: finops-app
  labels:
    app: mongo
    version: fixed
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mongo
      version: fixed
  template:
    metadata:
      labels:
        app: mongo
        version: fixed
    spec:
      containers:
      - name: mongo
        image: mongo:5.0-focal
        ports:
        - containerPort: 27017
        resources:
          requests:
            cpu: 250m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        env:
        - name: MONGO_INITDB_DATABASE
          value: "todoapp"
        - name: MONGO_INITDB_ROOT_USERNAME
          value: "admin"
        - name: MONGO_INITDB_ROOT_PASSWORD
          value: "admin123"
        volumeMounts:
        - name: mongo-storage
          mountPath: /data/db
        livenessProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
      volumes:
      - name: mongo-storage
        persistentVolumeClaim:
          claimName: mongo-pvc 