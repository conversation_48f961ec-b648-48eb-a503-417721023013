apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config-fixed
  namespace: finops-app
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }
    
    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        
        # Logging
        access_log /var/log/nginx/access.log;
        error_log /var/log/nginx/error.log;
        
        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        
        server {
            listen 80;
            server_name localhost;
            root /usr/share/nginx/html;
            index index.html index.htm;
            
            # Health check endpoint
            location /health {
                access_log off;
                return 200 '{"status":"healthy","service":"frontend","timestamp":"$time_iso8601","version":"1.1.0"}';
                add_header Content-Type application/json;
            }
            
            # Readiness check endpoint
            location /ready {
                access_log off;
                return 200 '{"status":"ready","service":"frontend","timestamp":"$time_iso8601"}';
                add_header Content-Type application/json;
            }
            
            # Liveness check endpoint
            location /live {
                access_log off;
                return 200 '{"status":"alive","service":"frontend","timestamp":"$time_iso8601"}';
                add_header Content-Type application/json;
            }
            
            # Handle React Router
            location / {
                try_files $uri $uri/ /index.html;
            }
            
            # Cache static assets
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
            
            # API proxy to backend
            location /api {
                proxy_pass http://backend-service-fixed:8080;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
        }
    } 