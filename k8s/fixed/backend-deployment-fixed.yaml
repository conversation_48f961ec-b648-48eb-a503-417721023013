apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-fixed
  namespace: finops-app
  labels:
    app: backend
    version: fixed
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backend
      version: fixed
  template:
    metadata:
      labels:
        app: backend
        version: fixed
    spec:
      containers:
      - name: backend
        image: backend:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 8080
        resources:
          requests:
            cpu: 250m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        env:
        - name: PORT
          value: "8080"
        - name: DB_URL
          value: "***************************************************************************"
        - name: NODE_ENV
          value: "production"
        livenessProbe:
          httpGet:
            path: /live
            port: 8080
          initialDelaySeconds: 120
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /live
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30 