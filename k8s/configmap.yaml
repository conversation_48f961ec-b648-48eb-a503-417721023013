apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: finops-app
data:
  DB_HOST: "mongo-service"
  DB_PORT: "27017"
  DB_NAME: "todoapp"
  API_URL: "http://backend-service:8080/api"
  NODE_ENV: "production"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: finops-app
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }
    
    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
        
        server {
            listen 80;
            server_name localhost;
            root /usr/share/nginx/html;
            index index.html index.htm;
            
            location / {
                try_files $uri $uri/ /index.html;
            }
            
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
            
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }
        }
    } 