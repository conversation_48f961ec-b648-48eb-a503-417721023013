apiVersion: v1
kind: Service
metadata:
  name: mongo-service-optimized
  namespace: finops-app
  labels:
    app: mongo
    version: optimized
spec:
  selector:
    app: mongo
    version: optimized
  ports:
  - port: 27017
    targetPort: 27017
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: backend-service-optimized
  namespace: finops-app
  labels:
    app: backend
    version: optimized
spec:
  selector:
    app: backend
    version: optimized
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service-optimized
  namespace: finops-app
  labels:
    app: frontend
    version: optimized
spec:
  selector:
    app: frontend
    version: optimized
  ports:
  - port: 80
    targetPort: 80
    nodePort: 30081  # Port différent pour éviter les conflits
  type: NodePort 