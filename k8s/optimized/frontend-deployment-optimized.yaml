apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-optimized
  namespace: finops-app
  labels:
    app: frontend
    version: optimized
spec:
  replicas: 1  # Réduit de 2 à 1
  selector:
    matchLabels:
      app: frontend
      version: optimized
  template:
    metadata:
      labels:
        app: frontend
        version: optimized
    spec:
      containers:
      - name: frontend
        image: frontend:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 80
        resources:
          requests:
            cpu: 100m      # Réduit de 250m
            memory: 128Mi  # Réduit de 256Mi
          limits:
            cpu: 250m      # Réduit de 500m
            memory: 256Mi  # Réduit de 512Mi
        env:
        - name: REACT_APP_API_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: API_URL
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-config 