apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-optimized
  namespace: finops-app
  labels:
    app: backend
    version: optimized
spec:
  replicas: 1  # Réduit de 2 à 1
  selector:
    matchLabels:
      app: backend
      version: optimized
  template:
    metadata:
      labels:
        app: backend
        version: optimized
    spec:
      containers:
      - name: backend
        image: backend:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 8080
        resources:
          requests:
            cpu: 250m      # Réduit de 500m
            memory: 256Mi  # Réduit de 512Mi
          limits:
            cpu: 500m      # Réduit de 1000m
            memory: 512Mi  # Réduit de 1Gi
        env:
        - name: PORT
          value: "8080"
        - name: DB_URL
          value: "mongodb://mongo-service:27017/todoapp"
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: NODE_ENV
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          failureThreshold: 30 