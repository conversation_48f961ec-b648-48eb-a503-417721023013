apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongo-optimized
  namespace: finops-app
  labels:
    app: mongo
    version: optimized
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mongo
      version: optimized
  template:
    metadata:
      labels:
        app: mongo
        version: optimized
    spec:
      containers:
      - name: mongo
        image: mongo:5.0-focal
        ports:
        - containerPort: 27017
        resources:
          requests:
            cpu: 250m      # Réduit de 500m
            memory: 256Mi  # Réduit de 512Mi
          limits:
            cpu: 500m      # Réduit de 1000m
            memory: 512Mi  # Réduit de 1Gi
        env:
        - name: MONGO_INITDB_DATABASE
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: DB_NAME
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: MONGO_INITDB_ROOT_USERNAME
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: MONGO_INITDB_ROOT_PASSWORD
        volumeMounts:
        - name: mongo-storage
          mountPath: /data/db
        livenessProbe:
          exec:
            command:
            - mongo
            - --eval
            - db.adminCommand('ping')
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - mongo
            - --eval
            - db.adminCommand('ping')
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: mongo-storage
        persistentVolumeClaim:
          claimName: mongo-pvc 