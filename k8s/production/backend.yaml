apiVersion: v1
kind: Secret
metadata:
  name: backend-secret
  namespace: finops-production
type: Opaque
data:
  jwt-secret: eW91ci1zdXBlci1zZWN1cmUtand0LXNlY3JldC1rZXktY2hhbmdlLWluLXByb2R1Y3Rpb24=
  db-url: ****************************************************************************************************************************
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-config
  namespace: finops-production
data:
  NODE_ENV: "production"
  PORT: "8080"
  LOG_LEVEL: "info"
  CORS_ORIGIN: "https://your-domain.com,https://www.your-domain.com"
  RATE_LIMIT_WINDOW_MS: "900000"
  RATE_LIMIT_MAX_REQUESTS: "100"
  RATE_LIMIT_AUTH_MAX_REQUESTS: "5"
  PERFORMANCE_MONITORING: "true"
  MEMORY_THRESHOLD_MB: "500"
  CPU_THRESHOLD_SECONDS: "5"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: finops-production
  labels:
    app: backend
    component: api
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
        component: api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: backend
        image: finops/backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: backend-secret
              key: jwt-secret
        - name: DB_URL
          valueFrom:
            secretKeyRef:
              name: backend-secret
              key: db-url
        envFrom:
        - configMapRef:
            name: backend-config
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: uploads
          mountPath: /app/uploads
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 10
      volumes:
      - name: logs
        emptyDir: {}
      - name: uploads
        persistentVolumeClaim:
          claimName: backend-uploads-pvc
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - backend
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backend-uploads-pvc
  namespace: finops-production
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
---
apiVersion: v1
kind: Service
metadata:
  name: backend
  namespace: finops-production
  labels:
    app: backend
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  - port: 9090
    targetPort: 9090
    name: metrics
  selector:
    app: backend
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa
  namespace: finops-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
