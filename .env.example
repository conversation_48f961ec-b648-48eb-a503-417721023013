# Environment Configuration for FinOps Todo App

# Backend Configuration
NODE_ENV=development
PORT=8080
DB_URL=mongodb://localhost:27017/todoapp

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8080/api
REACT_APP_ENV=development

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-here-change-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
CORS_ORIGIN=http://localhost:3000
FRONTEND_URL=http://localhost:3000

# Database Configuration
DB_HOST=localhost
DB_PORT=27017
DB_NAME=todoapp
DB_USER=
DB_PASSWORD=

# Monitoring Configuration
ENABLE_LOGGING=true
LOG_LEVEL=info
METRICS_PORT=9090

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000

# Test Configuration
TEST_DB_URL=mongodb://localhost:27017/todoapp_test
TEST_PORT=8081

# Kubernetes Configuration
K8S_NAMESPACE=finops-app
K8S_SERVICE_ACCOUNT=default

# FinOps Configuration
COST_TRACKING_ENABLED=true
RESOURCE_MONITORING_ENABLED=true