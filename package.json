{"name": "finops-project-tests", "version": "1.0.0", "description": "Tests Playwright pour le projet FinOps Kubernetes", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:report": "playwright show-report", "validate": "node scripts/validate-deployment.js && npm test", "install-browsers": "playwright install"}, "devDependencies": {"@playwright/test": "^1.54.1"}, "keywords": ["finops", "kubernetes", "playwright", "testing"], "author": "DevOps Team", "license": "MIT"}