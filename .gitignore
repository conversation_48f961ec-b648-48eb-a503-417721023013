# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Kubernetes
*.yaml.bak
*.yml.bak

# Test results
test-results/
playwright-report/
coverage/

# Build outputs
build/
dist/

# Database
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# IDE files
*.sublime-project
*.sublime-workspace

# Local configuration files
config/local.js
config/local.json

# Certificate files
*.pem
*.key
*.crt
*.csr

# Sensitive files
secrets/
private/

# Monitoring and metrics
prometheus/
grafana/

# Helm charts
charts/
*.tgz

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Local development
.local/
local/

# Documentation build
docs/_build/
site/

# Python (if any Python scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Java (if any Java components)
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# Temporary files
*.tmp
*.temp

# Lock files (keep package-lock.json for consistency)
yarn.lock

# Local storage
storage/
uploads/
downloads/

# Cache directories
.cache/
.npm/
.yarn/

# Runtime directories
run/
var/

# Application specific
backend/logs/
frontend/build/
k8s/secrets/
monitoring/data/

# Test artifacts
screenshots/
videos/
traces/

# Performance monitoring
*.perf
*.prof

# Memory dumps
*.hprof
*.dump

# Core dumps
core.*

# Application logs
app.log
error.log
access.log
debug.log
combined.log

# PM2 logs
pm2.log
pm2-error.log

# Forever logs
forever.log

# Nodemon
nodemon.json

# ESLint
.eslintrc.local.js

# Prettier
.prettierrc.local

# Jest
jest.config.local.js

# Webpack
webpack.config.local.js

# Babel
.babelrc.local

# TypeScript
tsconfig.local.json

# Rollup
rollup.config.local.js

# Vite
vite.config.local.js

# Svelte
svelte.config.local.js

# Vue
vue.config.local.js

# Angular
angular.json.local

# React
.env.local
.env.development.local
.env.test.local
.env.production.local

# Expo
.expo/
.expo-shared/

# React Native
.react-native/

# Cordova
platforms/
plugins/
www/

# Ionic
.ionic/

# Capacitor
.capacitor/

# NativeScript
hooks/
platforms/

# Xamarin
bin/
obj/

# Unity
[Ll]ibrary/
[Tt]emp/
[Oo]bj/
[Bb]uild/
[Bb]uilds/
Assets/AssetStoreTools*

# Unreal Engine
Binaries/
DerivedDataCache/
Intermediate/
Saved/
*.VC.db
*.opensdf
*.opendb
*.sdf
*.sln
*.suo
*.xcodeproj
*.xcworkspace
