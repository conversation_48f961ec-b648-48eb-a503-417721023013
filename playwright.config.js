// @ts-check
const { defineConfig, devices } = require('@playwright/test');

/**
 * Enhanced Playwright Configuration for FinOps Application Testing
 * @see https://playwright.dev/docs/test-configuration
 */
module.exports = defineConfig({
  testDir: './tests',

  /* Test organization */
  fullyParallel: false, // Disable for authentication tests that share state
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 3 : 1, // More retries for stability
  workers: process.env.CI ? 2 : 4, // Controlled parallelism

  /* Timeouts */
  timeout: 60000, // 60 seconds per test
  expect: {
    timeout: 10000 // 10 seconds for assertions
  },

  /* Global setup and teardown */
  globalSetup: require.resolve('./tests/global-setup.js'),
  globalTeardown: require.resolve('./tests/global-teardown.js'),

  /* Reporter configuration */
  reporter: [
    ['html', { outputFolder: 'playwright-report', open: 'never' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['list'],
    ['github'] // For CI/CD integration
  ],

  /* Output directories */
  outputDir: 'test-results/',

  /* Shared settings for all projects */
  use: {
    /* Base URLs */
    baseURL: process.env.FRONTEND_URL || 'http://localhost:3000',

    /* API testing */
    extraHTTPHeaders: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },

    /* Screenshots and videos */
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'retain-on-failure',

    /* Browser context */
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,

    /* Performance */
    actionTimeout: 15000,
    navigationTimeout: 30000,

    /* Locale and timezone */
    locale: 'en-US',
    timezoneId: 'Europe/Paris',
  },

  /* Test projects for different scenarios */
  projects: [
    /* Setup project for authentication */
    {
      name: 'setup',
      testMatch: /.*\.setup\.js/,
      teardown: 'cleanup',
    },

    /* Cleanup project */
    {
      name: 'cleanup',
      testMatch: /.*\.teardown\.js/,
    },

    /* Desktop browsers - authenticated tests */
    {
      name: 'chromium-auth',
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
      },
      dependencies: ['setup'],
      testMatch: /.*\.auth\.spec\.js/,
    },

    /* Desktop browsers - public tests */
    {
      name: 'chromium-public',
      use: { ...devices['Desktop Chrome'] },
      testMatch: /.*\.public\.spec\.js/,
    },

    /* API testing */
    {
      name: 'api-tests',
      use: {
        baseURL: process.env.API_URL || 'http://localhost:8080/api',
      },
      testMatch: /.*\.api\.spec\.js/,
      dependencies: ['setup'],
    },

    /* Performance testing */
    {
      name: 'performance',
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
      },
      dependencies: ['setup'],
      testMatch: /.*\.performance\.spec\.js/,
    },

    /* Security testing */
    {
      name: 'security',
      use: { ...devices['Desktop Chrome'] },
      testMatch: /.*\.security\.spec\.js/,
    },

    /* Cross-browser testing */
    {
      name: 'firefox',
      use: {
        ...devices['Desktop Firefox'],
        storageState: 'playwright/.auth/user.json',
      },
      dependencies: ['setup'],
      testMatch: /.*\.cross-browser\.spec\.js/,
    },

    {
      name: 'webkit',
      use: {
        ...devices['Desktop Safari'],
        storageState: 'playwright/.auth/user.json',
      },
      dependencies: ['setup'],
      testMatch: /.*\.cross-browser\.spec\.js/,
    },

    /* Mobile testing */
    {
      name: 'mobile-chrome',
      use: {
        ...devices['Pixel 5'],
        storageState: 'playwright/.auth/user.json',
      },
      dependencies: ['setup'],
      testMatch: /.*\.mobile\.spec\.js/,
    },

    {
      name: 'mobile-safari',
      use: {
        ...devices['iPhone 12'],
        storageState: 'playwright/.auth/user.json',
      },
      dependencies: ['setup'],
      testMatch: /.*\.mobile\.spec\.js/,
    },
  ],

  /* Web servers for testing */
  webServer: [
    {
      command: 'cd backend && npm start',
      url: 'http://localhost:8080/health',
      reuseExistingServer: !process.env.CI,
      timeout: 120000,
      env: {
        NODE_ENV: 'test',
        PORT: '8080',
        DB_URL: 'mongodb://localhost:27017/todoapp_test',
      },
    },
    {
      command: 'cd frontend && npm start',
      url: 'http://localhost:3000',
      reuseExistingServer: !process.env.CI,
      timeout: 120000,
      env: {
        REACT_APP_API_URL: 'http://localhost:8080/api',
        REACT_APP_ENV: 'test',
      },
    },
  ],
});