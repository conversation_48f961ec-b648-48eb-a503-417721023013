# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a FinOps Kubernetes optimization project featuring a MERN stack Todo application. The project demonstrates cost optimization techniques for Kubernetes deployments, achieving a 69.6% cost reduction (€43.20 → €13.14/month) through resource optimization.

### Architecture
- **Frontend**: React.js with <PERSON>in<PERSON> (port 80/30080)
- **Backend**: Node.js Express API (port 8080)
- **Database**: MongoDB (port 27017)
- **Deployment**: Kubernetes with Minikube
- **Monitoring**: Kubecost for FinOps analysis

## Essential Commands

### Development
```bash
# Frontend development
cd frontend && npm start                    # Runs on port 3000
cd frontend && npm run build               # Production build
cd frontend && npm test                    # Run tests

# Backend development  
cd backend && npm run dev                  # Development with nodemon
cd backend && npm start                    # Production mode
```

### Docker Operations
```bash
# Build images (must be in Minikube Docker env)
eval $(minikube docker-env)
docker build -t frontend:latest ./frontend
docker build -t backend:latest ./backend

# Local testing with docker-compose
docker-compose up                          # Full stack
docker-compose down                        # Cleanup
```

### Kubernetes Deployment
```bash
# Full deployment
./scripts/deploy.sh                       # Complete automated deployment

# Manual deployment steps
minikube start --cpus=4 --memory=6000 --driver=docker
eval $(minikube docker-env)
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmap.yaml k8s/secret.yaml k8s/pvc.yaml
kubectl apply -f k8s/mongo-deployment.yaml
kubectl apply -f k8s/backend-deployment.yaml
kubectl apply -f k8s/frontend-deployment.yaml  
kubectl apply -f k8s/services.yaml

# Access application
minikube service frontend-service -n finops-app
```

### Monitoring & Analysis
```bash
# FinOps cost analysis
python3 scripts/cost-calculator.py        # Generate cost report
python3 scripts/compare-costs.py          # Before/after comparison

# Kubecost access
kubectl port-forward --namespace kubecost deployment/kubecost-cost-analyzer 9090
# Then visit http://localhost:9090

# Resource monitoring
kubectl top pods -n finops-app
kubectl get pods -n finops-app -o wide
```

### Cleanup
```bash
./scripts/cleanup.sh                      # Full cleanup with confirmation
```

## Code Structure

### Frontend (React)
- `src/App.js`: Main application component with todo state management
- `src/components/`: React components (AddTodo, TodoList, TodoItem)
- `src/services/TodoService.js`: API communication layer using axios
- Uses Bootstrap for styling and React hooks for state management

### Backend (Node.js)
- `server.js`: Express server entry point with middleware and routing
- `app/models/todo.model.js`: Mongoose schema with auto-updating timestamps
- `app/controllers/todo.controller.js`: CRUD operations for todos
- `app/routes/todo.routes.js`: Express route definitions
- `app/config/db.config.js`: MongoDB connection configuration

### Kubernetes Configuration
- `k8s/`: Standard Kubernetes manifests for namespace, services, deployments
- `k8s/optimized/`: Cost-optimized versions with reduced resource requests
- Uses `finops-app` namespace for all resources
- PVC for MongoDB persistent storage

## FinOps Optimization Patterns

### Resource Optimization Applied
- **CPU requests**: Reduced from 2000m to 600m total (-70%)
- **Memory requests**: Reduced from 2048Mi to 640Mi total (-68.8%)
- **Replica reduction**: From 2 to 1 replica per service where appropriate
- **Multi-stage Docker builds**: Optimized image sizes

### Monitoring Setup
- Kubecost for real-time cost analysis
- Custom Python scripts for cost calculation using simulated pricing
- Resource usage tracking with kubectl top

### Key Metrics Tracked
- CPU/Memory requests vs actual usage
- Cost per application/pod/month
- Resource waste identification
- Optimization recommendations

## Testing & Validation

The project doesn't include formal test suites but validates deployment through:
- Health check endpoints (`/health` on backend)
- Functional testing via UI interaction
- Resource monitoring and cost analysis
- Kubernetes readiness/liveness probes

## Important Notes

- Always run `eval $(minikube docker-env)` before building images
- Use `finops-app` namespace for all Kubernetes resources
- The project uses simulated pricing in cost calculations
- Scripts expect to be run from the project root directory
- Frontend expects backend API at `http://localhost:8080/api` in local development