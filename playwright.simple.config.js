// @ts-check
const { defineConfig, devices } = require('@playwright/test');

/**
 * Simple Playwright Configuration for FinOps Application Testing
 * @see https://playwright.dev/docs/test-configuration
 */
module.exports = defineConfig({
  testDir: './tests',

  /* Test organization */
  fullyParallel: false,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 1,
  workers: process.env.CI ? 1 : 2,

  /* Timeouts */
  timeout: 60000,
  expect: {
    timeout: 10000
  },

  /* Reporter configuration */
  reporter: [
    ['html', { outputFolder: 'playwright-report', open: 'never' }],
    ['list']
  ],

  /* Output directories */
  outputDir: 'test-results/',

  /* Shared settings for all projects */
  use: {
    /* Base URLs */
    baseURL: 'http://localhost:3000',

    /* Screenshots and videos */
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'retain-on-failure',

    /* Browser context */
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,

    /* Performance */
    actionTimeout: 15000,
    navigationTimeout: 30000,

    /* Locale and timezone */
    locale: 'en-US',
    timezoneId: 'Europe/Paris',
  },

  /* Test projects for different scenarios */
  projects: [
    /* Desktop browsers - public tests */
    {
      name: 'chromium-public',
      use: { ...devices['Desktop Chrome'] },
      testMatch: /.*\.public\.spec\.js/,
    },
  ],
});
