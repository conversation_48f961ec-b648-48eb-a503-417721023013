const { test: base } = require('@playwright/test');

// Extend base test with custom fixtures
const test = base.extend({
  // Clean database before each test
  cleanDB: async ({ request }, use) => {
    // Clean the test database before each test
    try {
      await request.delete('http://localhost:8080/api/todos');
    } catch (error) {
      console.log('No todos to clean or API not ready');
    }
    
    await use();
    
    // Optionally clean after test as well
    try {
      await request.delete('http://localhost:8080/api/todos');
    } catch (error) {
      console.log('Cleanup failed or API not available');
    }
  },

  // Create sample todos for testing
  sampleTodos: async ({ request, cleanDB }, use) => {
    await cleanDB;
    
    const todos = [
      { title: 'Sample Todo 1', description: 'First sample todo', completed: false },
      { title: 'Sample Todo 2', description: 'Second sample todo', completed: true },
      { title: 'Sample Todo 3', description: 'Third sample todo', completed: false }
    ];

    const createdTodos = [];
    for (const todo of todos) {
      try {
        const response = await request.post('http://localhost:8080/api/todos', {
          data: todo
        });
        if (response.ok()) {
          createdTodos.push(await response.json());
        }
      } catch (error) {
        console.log('Failed to create sample todo:', error);
      }
    }

    await use(createdTodos);
  }
});

module.exports = { test };