const { test, expect } = require('@playwright/test');

test.describe('Budget Management', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to budget management page
    await page.goto('/finops/budgets');
    await page.waitForLoadState('networkidle');
  });
  
  test.describe('Budget Creation', () => {
    test('should create a new budget successfully', async ({ page }) => {
      // Click create budget button
      await page.click('[data-testid="create-budget"], button:has-text("Create Budget"), button:has-text("New Budget")');
      
      // Fill budget form
      const timestamp = Date.now();
      await page.fill('input[name="name"]', `Test Budget ${timestamp}`);
      await page.fill('textarea[name="description"]', 'Budget created during E2E testing');
      
      // Set budget amount
      await page.fill('input[name="total"], input[name="amount"]', '5000');
      
      // Select scope
      await page.selectOption('select[name="scope"], select[name="type"]', 'team');
      
      // Set date range
      const startDate = new Date().toISOString().split('T')[0];
      const endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      await page.fill('input[name="startDate"]', startDate);
      await page.fill('input[name="endDate"]', endDate);
      
      // Submit form
      await page.click('button[type="submit"], button:has-text("Create"), button:has-text("Save")');
      
      // Verify success
      await expect(page.locator('text=Budget created, text=Success')).toBeVisible();
      await expect(page.locator(`text=Test Budget ${timestamp}`)).toBeVisible();
    });
    
    test('should validate required fields', async ({ page }) => {
      // Click create budget button
      await page.click('[data-testid="create-budget"], button:has-text("Create Budget"), button:has-text("New Budget")');
      
      // Submit empty form
      await page.click('button[type="submit"], button:has-text("Create"), button:has-text("Save")');
      
      // Check for validation errors
      await expect(page.locator('.invalid-feedback, .error, .field-error')).toBeVisible();
      await expect(page.locator('text=required, text=Required')).toBeVisible();
    });
    
    test('should validate budget amount is positive', async ({ page }) => {
      // Click create budget button
      await page.click('[data-testid="create-budget"], button:has-text("Create Budget"), button:has-text("New Budget")');
      
      // Fill form with negative amount
      await page.fill('input[name="name"]', 'Invalid Budget');
      await page.fill('input[name="total"], input[name="amount"]', '-100');
      
      // Submit form
      await page.click('button[type="submit"], button:has-text("Create"), button:has-text("Save")');
      
      // Check for validation error
      await expect(page.locator('text=positive, text=greater than')).toBeVisible();
    });
  });
  
  test.describe('Budget List', () => {
    test('should display existing budgets', async ({ page }) => {
      // Check for budget list
      await expect(page.locator('[data-testid="budget-list"], .budget-table, table')).toBeVisible();
      
      // Check for budget columns
      await expect(page.locator('text=Name, text=Amount, text=Status')).toBeVisible();
    });
    
    test('should filter budgets by status', async ({ page }) => {
      // Look for status filter
      const statusFilter = page.locator('[data-testid="status-filter"], select[name="status"]');
      
      if (await statusFilter.isVisible()) {
        // Filter by active budgets
        await statusFilter.selectOption('active');
        
        // Wait for filter to apply
        await page.waitForLoadState('networkidle');
        
        // Verify filtered results
        await expect(page.locator('text=Active')).toBeVisible();
      }
    });
    
    test('should search budgets by name', async ({ page }) => {
      // Look for search input
      const searchInput = page.locator('[data-testid="budget-search"], input[placeholder*="search"], input[name="search"]');
      
      if (await searchInput.isVisible()) {
        // Search for a budget
        await searchInput.fill('Test Budget');
        
        // Wait for search results
        await page.waitForLoadState('networkidle');
        
        // Verify search results
        await expect(page.locator('text=Test Budget')).toBeVisible();
      }
    });
  });
  
  test.describe('Budget Details', () => {
    test('should view budget details', async ({ page }) => {
      // Click on first budget in list
      const firstBudget = page.locator('[data-testid="budget-item"], .budget-row, tr').first();
      
      if (await firstBudget.isVisible()) {
        await firstBudget.click();
        
        // Should show budget details
        await expect(page.locator('[data-testid="budget-details"], .budget-detail')).toBeVisible();
        
        // Check for budget information
        await expect(page.locator('text=Budget Details, text=Amount, text=Period')).toBeVisible();
      }
    });
    
    test('should display budget utilization', async ({ page }) => {
      // Navigate to a specific budget
      const firstBudget = page.locator('[data-testid="budget-item"], .budget-row, tr').first();
      
      if (await firstBudget.isVisible()) {
        await firstBudget.click();
        
        // Check for utilization information
        await expect(page.locator('text=Utilization, text=%, .progress-bar')).toBeVisible();
        
        // Check for spending breakdown
        await expect(page.locator('text=Current Spend, text=Remaining')).toBeVisible();
      }
    });
    
    test('should display budget alerts', async ({ page }) => {
      // Navigate to a specific budget
      const firstBudget = page.locator('[data-testid="budget-item"], .budget-row, tr').first();
      
      if (await firstBudget.isVisible()) {
        await firstBudget.click();
        
        // Check for alerts section
        await expect(page.locator('[data-testid="budget-alerts"], .alerts-section')).toBeVisible();
        
        // Check for alert thresholds
        await expect(page.locator('text=Alert, text=Threshold, text=%')).toBeVisible();
      }
    });
  });
  
  test.describe('Budget Editing', () => {
    test('should edit budget successfully', async ({ page }) => {
      // Find edit button for first budget
      const editButton = page.locator('[data-testid="edit-budget"], button:has-text("Edit")').first();
      
      if (await editButton.isVisible()) {
        await editButton.click();
        
        // Modify budget name
        const nameInput = page.locator('input[name="name"]');
        await nameInput.fill('Updated Budget Name');
        
        // Save changes
        await page.click('button[type="submit"], button:has-text("Save"), button:has-text("Update")');
        
        // Verify success
        await expect(page.locator('text=Budget updated, text=Success')).toBeVisible();
        await expect(page.locator('text=Updated Budget Name')).toBeVisible();
      }
    });
    
    test('should cancel budget editing', async ({ page }) => {
      // Find edit button for first budget
      const editButton = page.locator('[data-testid="edit-budget"], button:has-text("Edit")').first();
      
      if (await editButton.isVisible()) {
        await editButton.click();
        
        // Modify budget name
        await page.fill('input[name="name"]', 'This should not be saved');
        
        // Cancel editing
        await page.click('button:has-text("Cancel")');
        
        // Verify changes were not saved
        await expect(page.locator('text=This should not be saved')).not.toBeVisible();
      }
    });
  });
  
  test.describe('Budget Deletion', () => {
    test('should delete budget with confirmation', async ({ page }) => {
      // Find delete button for a budget
      const deleteButton = page.locator('[data-testid="delete-budget"], button:has-text("Delete")').first();
      
      if (await deleteButton.isVisible()) {
        // Get budget name before deletion
        const budgetRow = deleteButton.locator('..').locator('..');
        const budgetName = await budgetRow.locator('.budget-name, td').first().textContent();
        
        await deleteButton.click();
        
        // Confirm deletion
        await page.click('button:has-text("Confirm"), button:has-text("Yes"), button:has-text("Delete")');
        
        // Verify deletion
        await expect(page.locator('text=Budget deleted, text=Success')).toBeVisible();
        
        if (budgetName) {
          await expect(page.locator(`text=${budgetName.trim()}`)).not.toBeVisible();
        }
      }
    });
    
    test('should cancel budget deletion', async ({ page }) => {
      // Find delete button for a budget
      const deleteButton = page.locator('[data-testid="delete-budget"], button:has-text("Delete")').first();
      
      if (await deleteButton.isVisible()) {
        await deleteButton.click();
        
        // Cancel deletion
        await page.click('button:has-text("Cancel"), button:has-text("No")');
        
        // Verify budget still exists
        await expect(deleteButton).toBeVisible();
      }
    });
  });
  
  test.describe('Budget Alerts', () => {
    test('should configure budget alerts', async ({ page }) => {
      // Navigate to budget details
      const firstBudget = page.locator('[data-testid="budget-item"], .budget-row, tr').first();
      
      if (await firstBudget.isVisible()) {
        await firstBudget.click();
        
        // Look for alert configuration
        const alertConfig = page.locator('[data-testid="alert-config"], button:has-text("Configure Alerts")');
        
        if (await alertConfig.isVisible()) {
          await alertConfig.click();
          
          // Set alert threshold
          await page.fill('input[name="threshold"]', '80');
          
          // Save alert configuration
          await page.click('button:has-text("Save Alerts")');
          
          // Verify success
          await expect(page.locator('text=Alerts configured, text=Success')).toBeVisible();
        }
      }
    });
  });
});
