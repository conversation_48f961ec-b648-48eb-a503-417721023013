const { test, expect } = require('@playwright/test');

test.describe('Debug Login Issues', () => {
  test('Complete login diagnosis and fix', async ({ page, request }) => {
    console.log('🔍 Starting complete login diagnosis...');
    
    // Step 1: Check backend status
    console.log('\n1️⃣ Checking backend status...');
    const healthResponse = await request.get('http://localhost:8080/health');
    console.log('Backend status:', healthResponse.status());
    
    if (!healthResponse.ok()) {
      throw new Error('Backend is not responding');
    }
    
    // Step 2: Check what users exist in database
    console.log('\n2️⃣ Testing API login directly...');
    
    // Test with various credential combinations
    const credentialTests = [
      { email: '<EMAIL>', password: 'AdminPass123!' },
      { email: '<EMAIL>', password: 'AdminPass123!' },
      { email: 'finopsadmin', password: 'AdminPass123!' },
      { email: '<EMAIL>', password: 'TestPass123!' },
      { email: '<EMAIL>', password: 'SuperPass123!' }
    ];
    
    let workingCredentials = null;
    
    for (const creds of credentialTests) {
      console.log(`Testing: ${creds.email} / ${creds.password}`);
      try {
        const loginResponse = await request.post('http://localhost:8080/api/auth/login', {
          data: {
            identifier: creds.email,
            password: creds.password
          }
        });
        
        if (loginResponse.ok()) {
          const loginData = await loginResponse.json();
          console.log(`✅ SUCCESS with ${creds.email}`);
          console.log('User data:', loginData.data.user);
          workingCredentials = creds;
          break;
        } else {
          console.log(`❌ Failed: ${loginResponse.status()}`);
        }
      } catch (error) {
        console.log(`❌ Error: ${error.message}`);
      }
    }
    
    if (!workingCredentials) {
      console.log('\n🔧 No working credentials found. Creating new admin user...');
      
      // Create a new admin user
      const registerResponse = await request.post('http://localhost:8080/api/auth/register', {
        data: {
          username: 'debugadmin',
          email: '<EMAIL>',
          password: 'DebugPass123!',
          firstName: 'Debug',
          lastName: 'Admin',
          role: 'admin'
        }
      });
      
      if (registerResponse.ok()) {
        console.log('✅ Created new admin user: <EMAIL> / DebugPass123!');
        workingCredentials = { email: '<EMAIL>', password: 'DebugPass123!' };
      } else {
        console.log('❌ Failed to create admin user:', await registerResponse.text());
      }
    }
    
    // Step 3: Test frontend login
    console.log('\n3️⃣ Testing frontend login...');
    
    if (workingCredentials) {
      await page.goto('http://localhost:3000');
      
      // Wait for page to load
      await page.waitForLoadState('networkidle');
      
      // Check if we're on login page or need to navigate
      const currentUrl = page.url();
      console.log('Current URL:', currentUrl);
      
      if (!currentUrl.includes('/login')) {
        // Try to find login link or navigate directly
        try {
          await page.click('a[href*="login"], button:has-text("Login"), a:has-text("Login")');
        } catch {
          await page.goto('http://localhost:3000/login');
        }
      }
      
      await page.waitForLoadState('networkidle');
      
      // Fill login form
      console.log('Filling login form...');
      
      // Try different selectors for email field
      const emailSelectors = [
        'input[name="identifier"]',
        'input[name="email"]',
        'input[type="email"]',
        'input[placeholder*="email" i]',
        'input[placeholder*="username" i]'
      ];
      
      let emailField = null;
      for (const selector of emailSelectors) {
        try {
          emailField = page.locator(selector);
          if (await emailField.count() > 0) {
            console.log(`Found email field with selector: ${selector}`);
            break;
          }
        } catch (e) {
          continue;
        }
      }
      
      if (emailField && await emailField.count() > 0) {
        await emailField.fill(workingCredentials.email);
        console.log(`✅ Filled email: ${workingCredentials.email}`);
      } else {
        console.log('❌ Could not find email field');
        // Take screenshot for debugging
        await page.screenshot({ path: 'debug-login-form.png' });
      }
      
      // Try different selectors for password field
      const passwordSelectors = [
        'input[name="password"]',
        'input[type="password"]'
      ];
      
      let passwordField = null;
      for (const selector of passwordSelectors) {
        try {
          passwordField = page.locator(selector);
          if (await passwordField.count() > 0) {
            console.log(`Found password field with selector: ${selector}`);
            break;
          }
        } catch (e) {
          continue;
        }
      }
      
      if (passwordField && await passwordField.count() > 0) {
        await passwordField.fill(workingCredentials.password);
        console.log('✅ Filled password');
      } else {
        console.log('❌ Could not find password field');
      }
      
      // Submit form
      const submitSelectors = [
        'button[type="submit"]',
        'button:has-text("Sign In")',
        'button:has-text("Login")',
        'input[type="submit"]'
      ];
      
      let submitButton = null;
      for (const selector of submitSelectors) {
        try {
          submitButton = page.locator(selector);
          if (await submitButton.count() > 0) {
            console.log(`Found submit button with selector: ${selector}`);
            break;
          }
        } catch (e) {
          continue;
        }
      }
      
      if (submitButton && await submitButton.count() > 0) {
        console.log('Clicking submit button...');
        await submitButton.click();
        
        // Wait for navigation or error message
        await page.waitForTimeout(3000);
        
        const newUrl = page.url();
        console.log('URL after login attempt:', newUrl);
        
        // Check for success (dashboard) or error
        if (newUrl.includes('/dashboard') || newUrl.includes('/home')) {
          console.log('✅ LOGIN SUCCESSFUL! Redirected to dashboard');
        } else {
          // Check for error messages
          const errorSelectors = [
            '.error',
            '.alert-danger',
            '[class*="error"]',
            'text="Invalid credentials"',
            'text="Login failed"'
          ];
          
          for (const selector of errorSelectors) {
            try {
              const errorElement = page.locator(selector);
              if (await errorElement.count() > 0) {
                const errorText = await errorElement.textContent();
                console.log(`❌ Error found: ${errorText}`);
              }
            } catch (e) {
              continue;
            }
          }
          
          // Take screenshot for debugging
          await page.screenshot({ path: 'debug-login-error.png' });
        }
      } else {
        console.log('❌ Could not find submit button');
        await page.screenshot({ path: 'debug-no-submit.png' });
      }
    }
    
    console.log('\n📋 DIAGNOSIS COMPLETE');
    console.log('Working credentials:', workingCredentials);
  });
});
