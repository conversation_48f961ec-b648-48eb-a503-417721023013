const { test, expect } = require('@playwright/test');

test.describe('Complete FinOps Application Features', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('http://localhost:3000/login');
    await page.fill('input[name="identifier"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'AdminPass123!');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  });

  test('Dashboard displays budget metrics and alerts', async ({ page }) => {
    console.log('🏠 Testing Dashboard functionality...');
    
    // Verify dashboard loads
    await expect(page.locator('h1')).toContainText('FinOps Dashboard');
    
    // Check for summary cards
    const summaryCards = page.locator('.card.border-left-primary, .card.border-left-success, .card.border-left-info, .card.border-left-warning');
    await expect(summaryCards).toHaveCount(4);
    
    // Check for budget section
    const budgetSection = page.locator('text=Active Budgets').locator('..');
    await expect(budgetSection).toBeVisible();
    
    // Check refresh button works
    await page.click('button:has-text("Refresh")');
    await page.waitForTimeout(1000);
    
    console.log('✅ Dashboard functionality verified');
  });

  test('Budget Management - Create, Edit, Delete workflow', async ({ page }) => {
    console.log('💰 Testing Budget Management workflow...');
    
    // Navigate to budget management
    await page.goto('http://localhost:3000/budgets');
    await expect(page.locator('h1')).toContainText('Budget Management');
    
    // Create new budget
    await page.click('button:has-text("Create Budget")');
    await expect(page.locator('text=Create New Budget')).toBeVisible();
    
    // Fill budget form
    await page.fill('input[name="name"]', 'Test Budget E2E');
    await page.fill('input[type="number"]', '5000');
    await page.fill('textarea', 'Test budget for E2E testing');
    await page.fill('input[placeholder*="team"]', 'QA Team');
    
    // Set dates
    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
    await page.fill('input[type="date"]', today.toISOString().split('T')[0]);
    await page.fill('input[type="date"]:nth-of-type(2)', nextMonth.toISOString().split('T')[0]);
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Wait for success message
    await expect(page.locator('.alert-success')).toBeVisible();
    await expect(page.locator('.alert-success')).toContainText('Budget created successfully');
    
    // Verify budget appears in list
    await expect(page.locator('text=Test Budget E2E')).toBeVisible();
    
    // Test edit functionality
    await page.click('.dropdown-toggle');
    await page.click('text=Edit');
    
    // Modify budget
    await page.fill('input[name="name"]', 'Test Budget E2E Updated');
    await page.click('button:has-text("Update Budget")');
    
    // Verify update
    await expect(page.locator('.alert-success')).toContainText('Budget updated successfully');
    await expect(page.locator('text=Test Budget E2E Updated')).toBeVisible();
    
    // Test delete functionality
    await page.click('.dropdown-toggle');
    await page.click('text=Delete');
    
    // Confirm deletion
    await expect(page.locator('.modal')).toBeVisible();
    await page.click('button:has-text("Delete Budget")');
    
    // Verify deletion
    await expect(page.locator('.alert-success')).toContainText('Budget deleted successfully');
    
    console.log('✅ Budget Management workflow completed');
  });

  test('Cost Analysis features and filtering', async ({ page }) => {
    console.log('📊 Testing Cost Analysis features...');
    
    // Navigate to cost analysis
    await page.goto('http://localhost:3000/cost-analysis');
    await expect(page.locator('h1')).toContainText('Cost Analysis');
    
    // Test filters
    await page.selectOption('select[value="day"]', 'week');
    await page.selectOption('select:has(option[value="team"])', 'team');
    await page.fill('input[placeholder*="team"]', 'Development');
    
    // Wait for data to load
    await page.waitForTimeout(2000);
    
    // Test export functionality
    await page.click('button:has-text("Export CSV")');
    await page.waitForTimeout(1000);
    
    // Test add cost entry
    await page.click('button:has-text("Add Cost Entry")');
    await expect(page.locator('text=Add Cost Entry')).toBeVisible();
    
    // Close form
    await page.click('.btn-close');
    
    console.log('✅ Cost Analysis features verified');
  });

  test('Navigation and responsive design', async ({ page }) => {
    console.log('🧭 Testing Navigation and responsive design...');
    
    // Test navigation links
    const navLinks = [
      { text: 'Dashboard', url: '/dashboard' },
      { text: 'Cost Analysis', url: '/cost-analysis' },
      { text: 'Budgets', url: '/budgets' },
      { text: 'Reports', url: '/reports' }
    ];
    
    for (const link of navLinks) {
      await page.click(`a:has-text("${link.text}")`);
      await expect(page).toHaveURL(new RegExp(link.url));
      await page.waitForLoadState('networkidle');
    }
    
    // Test mobile responsiveness
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('http://localhost:3000/dashboard');
    
    // Check if content is still accessible
    await expect(page.locator('h1')).toBeVisible();
    
    // Reset viewport
    await page.setViewportSize({ width: 1280, height: 720 });
    
    console.log('✅ Navigation and responsive design verified');
  });

  test('Error handling and validation', async ({ page }) => {
    console.log('⚠️ Testing Error handling and validation...');
    
    // Test budget form validation
    await page.goto('http://localhost:3000/budgets');
    await page.click('button:has-text("Create Budget")');
    
    // Try to submit empty form
    await page.click('button[type="submit"]');
    
    // Check for validation messages
    const nameInput = page.locator('input[name="name"]');
    await expect(nameInput).toHaveAttribute('required');
    
    // Test invalid amount
    await page.fill('input[name="name"]', 'Test');
    await page.fill('input[type="number"]', '-100');
    await page.click('button[type="submit"]');
    
    // Should not allow negative amounts
    const amountInput = page.locator('input[type="number"]');
    await expect(amountInput).toHaveAttribute('min', '0');
    
    console.log('✅ Error handling and validation verified');
  });

  test('Data persistence across page refreshes', async ({ page }) => {
    console.log('💾 Testing Data persistence...');
    
    // Create a budget
    await page.goto('http://localhost:3000/budgets');
    await page.click('button:has-text("Create Budget")');
    
    await page.fill('input[name="name"]', 'Persistence Test Budget');
    await page.fill('input[type="number"]', '3000');
    
    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
    await page.fill('input[type="date"]', today.toISOString().split('T')[0]);
    await page.fill('input[type="date"]:nth-of-type(2)', nextMonth.toISOString().split('T')[0]);
    
    await page.click('button[type="submit"]');
    await expect(page.locator('.alert-success')).toBeVisible();
    
    // Refresh page
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Verify budget still exists
    await expect(page.locator('text=Persistence Test Budget')).toBeVisible();
    
    // Clean up - delete the test budget
    await page.click('.dropdown-toggle');
    await page.click('text=Delete');
    await page.click('button:has-text("Delete Budget")');
    
    console.log('✅ Data persistence verified');
  });

  test('Authentication state management', async ({ page }) => {
    console.log('🔐 Testing Authentication state management...');
    
    // Verify user is logged in
    await page.goto('http://localhost:3000/dashboard');
    await expect(page).toHaveURL(/dashboard/);
    
    // Check localStorage has auth data
    const authData = await page.evaluate(() => {
      return {
        token: !!localStorage.getItem('token'),
        user: !!localStorage.getItem('user')
      };
    });
    
    expect(authData.token).toBeTruthy();
    expect(authData.user).toBeTruthy();
    
    // Test session persistence after page refresh
    await page.reload();
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL(/dashboard/);
    
    console.log('✅ Authentication state management verified');
  });
});
