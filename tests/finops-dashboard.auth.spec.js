const { test, expect } = require('@playwright/test');

test.describe('FinOps Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to FinOps dashboard
    await page.goto('/finops/dashboard');
    await page.waitForLoadState('networkidle');
  });
  
  test.describe('Dashboard Overview', () => {
    test('should display cost overview widgets', async ({ page }) => {
      // Check for main cost metrics
      await expect(page.locator('[data-testid="total-cost"], .cost-metric')).toBeVisible();
      await expect(page.locator('text=Total Cost, text=€')).toBeVisible();
      
      // Check for cost breakdown
      await expect(page.locator('text=CPU Cost, text=Memory Cost, text=Storage Cost')).toBeVisible();
    });
    
    test('should display cost trends chart', async ({ page }) => {
      // Look for chart container
      await expect(page.locator('[data-testid="cost-trends"], .chart-container, canvas')).toBeVisible();
      
      // Check for chart legend or labels
      await expect(page.locator('text=Cost Trends, text=Daily, text=Weekly')).toBeVisible();
    });
    
    test('should display team cost breakdown', async ({ page }) => {
      // Check for team breakdown section
      await expect(page.locator('[data-testid="team-breakdown"], .team-costs')).toBeVisible();
      
      // Look for team names or cost data
      await expect(page.locator('text=Team, text=frontend-team, text=backend-team')).toBeVisible();
    });
    
    test('should display top cost resources', async ({ page }) => {
      // Check for top resources section
      await expect(page.locator('[data-testid="top-resources"], .top-resources')).toBeVisible();
      
      // Look for resource information
      await expect(page.locator('text=Top Resources, text=deployment, text=statefulset')).toBeVisible();
    });
  });
  
  test.describe('Dashboard Filters', () => {
    test('should filter data by date range', async ({ page }) => {
      // Look for date picker or filter controls
      const dateFilter = page.locator('[data-testid="date-filter"], input[type="date"], .date-picker');
      
      if (await dateFilter.isVisible()) {
        // Set date range
        await dateFilter.first().fill('2025-07-01');
        
        // Apply filter
        await page.click('button:has-text("Apply"), button:has-text("Filter")');
        
        // Wait for data to update
        await page.waitForLoadState('networkidle');
        
        // Verify data updated
        await expect(page.locator('.loading, .spinner')).not.toBeVisible();
      }
    });
    
    test('should filter data by team', async ({ page }) => {
      // Look for team filter
      const teamFilter = page.locator('[data-testid="team-filter"], select[name="team"]');
      
      if (await teamFilter.isVisible()) {
        // Select a team
        await teamFilter.selectOption('frontend-team');
        
        // Wait for data to update
        await page.waitForLoadState('networkidle');
        
        // Verify filtered data
        await expect(page.locator('text=frontend-team')).toBeVisible();
      }
    });
    
    test('should filter data by environment', async ({ page }) => {
      // Look for environment filter
      const envFilter = page.locator('[data-testid="env-filter"], select[name="environment"]');
      
      if (await envFilter.isVisible()) {
        // Select environment
        await envFilter.selectOption('production');
        
        // Wait for data to update
        await page.waitForLoadState('networkidle');
        
        // Verify filtered data
        await expect(page.locator('text=production')).toBeVisible();
      }
    });
  });
  
  test.describe('Dashboard Interactions', () => {
    test('should refresh dashboard data', async ({ page }) => {
      // Look for refresh button
      const refreshButton = page.locator('[data-testid="refresh"], button:has-text("Refresh")');
      
      if (await refreshButton.isVisible()) {
        await refreshButton.click();
        
        // Wait for refresh to complete
        await page.waitForLoadState('networkidle');
        
        // Verify data is still displayed
        await expect(page.locator('[data-testid="total-cost"], .cost-metric')).toBeVisible();
      }
    });
    
    test('should export dashboard data', async ({ page }) => {
      // Look for export button
      const exportButton = page.locator('[data-testid="export"], button:has-text("Export")');
      
      if (await exportButton.isVisible()) {
        // Start download
        const downloadPromise = page.waitForEvent('download');
        await exportButton.click();
        
        // Wait for download
        const download = await downloadPromise;
        
        // Verify download
        expect(download.suggestedFilename()).toMatch(/cost.*\.(csv|json|xlsx)$/);
      }
    });
    
    test('should navigate to detailed views', async ({ page }) => {
      // Click on a cost metric to drill down
      const costMetric = page.locator('[data-testid="total-cost"], .cost-metric').first();
      
      if (await costMetric.isVisible()) {
        await costMetric.click();
        
        // Should navigate to detailed view or show modal
        await expect(page.locator('.modal, [data-testid="cost-details"]')).toBeVisible();
      }
    });
  });
  
  test.describe('Real-time Updates', () => {
    test('should update data automatically', async ({ page }) => {
      // Get initial cost value
      const costElement = page.locator('[data-testid="total-cost"] .value, .cost-value').first();
      
      if (await costElement.isVisible()) {
        const initialValue = await costElement.textContent();
        
        // Wait for potential auto-refresh
        await page.waitForTimeout(5000);
        
        // Check if value might have updated (or at least element is still there)
        await expect(costElement).toBeVisible();
        
        // In a real scenario, you might trigger an action that causes cost changes
        // and then verify the dashboard updates accordingly
      }
    });
  });
  
  test.describe('Responsive Design', () => {
    test('should display correctly on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Reload to apply mobile layout
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Check that main elements are still visible
      await expect(page.locator('[data-testid="total-cost"], .cost-metric')).toBeVisible();
      
      // Check for mobile navigation
      const mobileMenu = page.locator('[data-testid="mobile-menu"], .navbar-toggler');
      if (await mobileMenu.isVisible()) {
        await mobileMenu.click();
        await expect(page.locator('.navbar-nav, .mobile-nav')).toBeVisible();
      }
    });
    
    test('should display correctly on tablet devices', async ({ page }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      
      // Reload to apply tablet layout
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Check that dashboard elements are properly arranged
      await expect(page.locator('[data-testid="total-cost"], .cost-metric')).toBeVisible();
      await expect(page.locator('[data-testid="cost-trends"], .chart-container')).toBeVisible();
    });
  });
});
