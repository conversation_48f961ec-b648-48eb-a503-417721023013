const { test, expect } = require('@playwright/test');

test.describe('Security Testing', () => {
  test.describe('Authentication Security', () => {
    test('should prevent SQL injection in login', async ({ request }) => {
      const maliciousPayloads = [
        "admin'; DROP TABLE users; --",
        "' OR '1'='1",
        "admin' OR 1=1 --",
        "'; UNION SELECT * FROM users --"
      ];
      
      for (const payload of maliciousPayloads) {
        const response = await request.post('/auth/login', {
          data: {
            identifier: payload,
            password: 'password'
          }
        });
        
        // Should not succeed with SQL injection
        expect(response.status()).toBe(401);
        const data = await response.json();
        expect(data.success).toBeFalsy();
      }
    });
    
    test('should prevent NoSQL injection', async ({ request }) => {
      const maliciousPayloads = [
        { "$ne": null },
        { "$gt": "" },
        { "$regex": ".*" },
        { "$where": "this.password" }
      ];
      
      for (const payload of maliciousPayloads) {
        const response = await request.post('/auth/login', {
          data: {
            identifier: payload,
            password: payload
          }
        });
        
        // Should not succeed with NoSQL injection
        expect(response.status()).toBe(400);
      }
    });
    
    test('should enforce rate limiting', async ({ request }) => {
      // Make multiple failed login attempts
      let rateLimited = false;
      
      for (let i = 0; i < 10; i++) {
        const response = await request.post('/auth/login', {
          data: {
            identifier: '<EMAIL>',
            password: 'wrongpassword'
          }
        });
        
        if (response.status() === 429) {
          rateLimited = true;
          break;
        }
        
        // Wait a bit between attempts
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      expect(rateLimited).toBeTruthy();
    });
    
    test('should validate JWT tokens properly', async ({ request }) => {
      const invalidTokens = [
        'invalid-token',
        'Bearer invalid',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature',
        ''
      ];
      
      for (const token of invalidTokens) {
        const response = await request.get('/finops/dashboard', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        expect(response.status()).toBe(401);
      }
    });
  });
  
  test.describe('Input Validation', () => {
    test('should sanitize XSS attempts in registration', async ({ request }) => {
      const xssPayloads = [
        '<script>alert("xss")</script>',
        'javascript:alert("xss")',
        '<img src="x" onerror="alert(1)">',
        '"><script>alert("xss")</script>'
      ];
      
      for (const payload of xssPayloads) {
        const response = await request.post('/auth/register', {
          data: {
            username: payload,
            email: '<EMAIL>',
            password: 'ValidPass123!',
            firstName: payload,
            lastName: payload
          }
        });
        
        // Should either reject or sanitize the input
        if (response.ok()) {
          const data = await response.json();
          // Check that XSS payload was sanitized
          expect(data.data.user.firstName).not.toContain('<script>');
          expect(data.data.user.lastName).not.toContain('<script>');
        } else {
          // Should reject malicious input
          expect(response.status()).toBe(400);
        }
      }
    });
    
    test('should validate email format', async ({ request }) => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'test@',
        '<EMAIL>',
        'test@example',
        ''
      ];
      
      for (const email of invalidEmails) {
        const response = await request.post('/auth/register', {
          data: {
            username: 'testuser',
            email: email,
            password: 'ValidPass123!',
            firstName: 'Test',
            lastName: 'User'
          }
        });
        
        expect(response.status()).toBe(400);
        const data = await response.json();
        expect(data.success).toBeFalsy();
      }
    });
    
    test('should enforce password complexity', async ({ request }) => {
      const weakPasswords = [
        'password',
        '123456',
        'abc',
        'PASSWORD',
        'password123',
        'Password',
        ''
      ];
      
      for (const password of weakPasswords) {
        const response = await request.post('/auth/register', {
          data: {
            username: 'testuser',
            email: '<EMAIL>',
            password: password,
            firstName: 'Test',
            lastName: 'User'
          }
        });
        
        expect(response.status()).toBe(400);
        const data = await response.json();
        expect(data.success).toBeFalsy();
      }
    });
  });
  
  test.describe('Authorization', () => {
    test('should enforce role-based access control', async ({ request }) => {
      // Login as regular user
      const loginResponse = await request.post('/auth/login', {
        data: {
          identifier: '<EMAIL>',
          password: 'TestPass123!'
        }
      });
      
      if (loginResponse.ok()) {
        const loginData = await loginResponse.json();
        const userToken = loginData.data.token;
        
        // Try to access admin endpoints
        const adminEndpoints = [
          '/admin/users',
          '/admin/settings',
          '/admin/logs'
        ];
        
        for (const endpoint of adminEndpoints) {
          const response = await request.get(endpoint, {
            headers: {
              'Authorization': `Bearer ${userToken}`
            }
          });
          
          // Should be denied
          expect([401, 403, 404]).toContain(response.status());
        }
      }
    });
    
    test('should protect sensitive endpoints', async ({ request }) => {
      const sensitiveEndpoints = [
        '/finops/dashboard',
        '/finops/budgets',
        '/finops/cost-analysis',
        '/todos'
      ];
      
      for (const endpoint of sensitiveEndpoints) {
        const response = await request.get(endpoint);
        
        // Should require authentication
        expect(response.status()).toBe(401);
      }
    });
  });
  
  test.describe('Data Protection', () => {
    test('should not expose sensitive data in responses', async ({ request }) => {
      // Login to get a valid response
      const loginResponse = await request.post('/auth/login', {
        data: {
          identifier: '<EMAIL>',
          password: 'AdminPass123!'
        }
      });
      
      expect(loginResponse.ok()).toBeTruthy();
      const loginData = await loginResponse.json();
      
      // Check that password is not exposed
      expect(loginData.data.user.password).toBeUndefined();
      expect(JSON.stringify(loginData)).not.toContain('password');
      
      // Get user profile
      const profileResponse = await request.get('/auth/profile', {
        headers: {
          'Authorization': `Bearer ${loginData.data.token}`
        }
      });
      
      expect(profileResponse.ok()).toBeTruthy();
      const profileData = await profileResponse.json();
      
      // Check that sensitive fields are not exposed
      expect(profileData.data.user.password).toBeUndefined();
      expect(profileData.data.user.passwordResetToken).toBeUndefined();
    });
    
    test('should handle file upload security', async ({ request }) => {
      // Test malicious file upload attempts
      const maliciousFiles = [
        { name: 'test.php', content: '<?php system($_GET["cmd"]); ?>' },
        { name: 'test.jsp', content: '<% Runtime.getRuntime().exec(request.getParameter("cmd")); %>' },
        { name: 'test.exe', content: 'MZ\x90\x00' }
      ];
      
      for (const file of maliciousFiles) {
        // If there's a file upload endpoint, test it
        // This is a placeholder for actual file upload testing
        console.log(`Would test malicious file: ${file.name}`);
      }
    });
  });
  
  test.describe('Security Headers', () => {
    test('should include security headers', async ({ request }) => {
      const response = await request.get('/health');
      const headers = response.headers();
      
      // Check for important security headers
      expect(headers['x-content-type-options']).toBe('nosniff');
      expect(headers['x-frame-options']).toBeDefined();
      expect(headers['x-xss-protection']).toBeDefined();
      expect(headers['strict-transport-security']).toBeDefined();
    });
    
    test('should prevent clickjacking', async ({ request }) => {
      const response = await request.get('/');
      const headers = response.headers();
      
      // Should have X-Frame-Options or CSP frame-ancestors
      const hasFrameProtection = 
        headers['x-frame-options'] === 'DENY' || 
        headers['x-frame-options'] === 'SAMEORIGIN' ||
        (headers['content-security-policy'] && 
         headers['content-security-policy'].includes('frame-ancestors'));
      
      expect(hasFrameProtection).toBeTruthy();
    });
  });
  
  test.describe('Session Security', () => {
    test('should invalidate tokens on logout', async ({ request }) => {
      // Login
      const loginResponse = await request.post('/auth/login', {
        data: {
          identifier: '<EMAIL>',
          password: 'AdminPass123!'
        }
      });
      
      expect(loginResponse.ok()).toBeTruthy();
      const loginData = await loginResponse.json();
      const token = loginData.data.token;
      
      // Verify token works
      const profileResponse = await request.get('/auth/profile', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      expect(profileResponse.ok()).toBeTruthy();
      
      // Logout (if logout endpoint exists)
      const logoutResponse = await request.post('/auth/logout', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      // If logout is implemented, token should be invalidated
      if (logoutResponse.ok()) {
        const profileResponse2 = await request.get('/auth/profile', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        expect(profileResponse2.status()).toBe(401);
      }
    });
  });
});
