const { test, expect } = require('@playwright/test');

test.describe('Final FinOps Application Validation', () => {
  test('Complete application functionality test', async ({ page }) => {
    console.log('🎯 Final validation of FinOps application...');

    // Step 1: Login
    console.log('🔐 Testing login...');
    await page.goto('http://localhost:3000/login');
    await page.fill('input[name="identifier"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'AdminPass123!');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
    console.log('✅ Login successful');

    // Step 2: Dashboard verification
    console.log('📊 Testing Dashboard...');
    await expect(page.locator('h1')).toContainText('FinOps Dashboard');
    await expect(page.locator('text=Active Budgets')).toBeVisible();
    console.log('✅ Dashboard loads correctly');

    // Step 3: Budget Management
    console.log('💰 Testing Budget Management...');
    await page.goto('http://localhost:3000/budgets');
    await page.waitForLoadState('networkidle');
    await expect(page.locator('h1')).toContainText('Budget Management');
    await expect(page.locator('button:has-text("Create Budget")')).toBeVisible();
    console.log('✅ Budget Management accessible');

    // Step 4: Cost Analysis
    console.log('📈 Testing Cost Analysis...');
    await page.goto('http://localhost:3000/cost-analysis');
    await page.waitForLoadState('networkidle');
    await expect(page.locator('h1')).toContainText('Cost Analysis');
    console.log('✅ Cost Analysis accessible');

    // Step 5: Reports
    console.log('📋 Testing Reports...');
    await page.goto('http://localhost:3000/reports');
    await page.waitForLoadState('networkidle');
    // Should load without errors
    const heading = page.locator('h1, h2, h3');
    await expect(heading).toBeVisible();
    console.log('✅ Reports accessible');

    // Step 6: Navigation test
    console.log('🧭 Testing Navigation...');
    const pages = [
      { name: 'Dashboard', url: '/dashboard' },
      { name: 'Budgets', url: '/budgets' },
      { name: 'Cost Analysis', url: '/cost-analysis' },
      { name: 'Reports', url: '/reports' }
    ];

    for (const pageInfo of pages) {
      await page.goto(`http://localhost:3000${pageInfo.url}`);
      await page.waitForLoadState('networkidle');
      expect(page.url()).toContain(pageInfo.url);
      console.log(`✅ ${pageInfo.name} navigation works`);
    }

    // Step 7: Authentication persistence
    console.log('🔐 Testing authentication persistence...');
    await page.goto('http://localhost:3000/dashboard');
    await page.reload();
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL(/dashboard/);

    const authData = await page.evaluate(() => {
      return {
        token: !!localStorage.getItem('token'),
        user: !!localStorage.getItem('user')
      };
    });

    expect(authData.token).toBeTruthy();
    expect(authData.user).toBeTruthy();
    console.log('✅ Authentication persists');

    // Step 8: Responsive design test
    console.log('📱 Testing responsive design...');
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    await expect(page.locator('h1')).toBeVisible();
    console.log('✅ Mobile view works');

    // Reset viewport
    await page.setViewportSize({ width: 1280, height: 720 });

    console.log('🎉 FINAL VALIDATION COMPLETE!');
    console.log('✅ All core features are working correctly');
    console.log('✅ Application is ready for production use');
  });

  test('Budget creation workflow validation', async ({ page }) => {
    console.log('💼 Testing budget creation workflow...');

    // Login
    await page.goto('http://localhost:3000/login');
    await page.fill('input[name="identifier"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'AdminPass123!');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');

    // Go to budget management
    await page.goto('http://localhost:3000/budgets');
    await page.waitForLoadState('networkidle');

    // Open create form
    await page.click('button:has-text("Create Budget")');
    await page.waitForSelector('form');

    // Verify form elements exist
    await expect(page.locator('input[placeholder*="name"], input[name="name"]')).toBeVisible();
    await expect(page.locator('input[type="number"]')).toBeVisible();
    await expect(page.locator('input[type="date"]')).toHaveCount(2);
    await expect(page.locator('button[type="submit"]')).toBeVisible();

    console.log('✅ Budget creation form is functional');

    // Close form
    await page.click('.btn-close, button:has-text("Cancel")');

    console.log('✅ Budget workflow validation complete');
  });

  test('API connectivity and data flow', async ({ page, request }) => {
    console.log('🔌 Testing API connectivity...');

    // Test backend health
    const healthResponse = await request.get('http://localhost:8080/health');
    expect(healthResponse.ok()).toBeTruthy();
    console.log('✅ Backend API is healthy');

    // Login to get token
    await page.goto('http://localhost:3000/login');
    await page.fill('input[name="identifier"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'AdminPass123!');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');

    // Get token from localStorage
    const token = await page.evaluate(() => localStorage.getItem('token'));
    expect(token).toBeTruthy();

    // Test authenticated API call
    const dashboardResponse = await request.get('http://localhost:8080/api/finops/dashboard', {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    expect(dashboardResponse.ok()).toBeTruthy();
    console.log('✅ Authenticated API calls work');

    // Test budget API
    const budgetResponse = await request.get('http://localhost:8080/api/finops/budgets', {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    expect(budgetResponse.ok()).toBeTruthy();
    console.log('✅ Budget API endpoints work');

    console.log('✅ API connectivity validation complete');
  });
});
