const { test, expect } = require('@playwright/test');

test.describe('Final Complete Validation', () => {
  test('Complete user journey - registration to dashboard', async ({ page, request }) => {
    console.log('🎯 FINAL COMPLETE VALIDATION');
    
    // Step 1: Test backend health
    console.log('\n1️⃣ Backend Health Check...');
    const health = await request.get('http://localhost:8080/health');
    expect(health.ok()).toBeTruthy();
    console.log('✅ Backend healthy');
    
    // Step 2: Clear browser state
    await page.goto('http://localhost:3000');
    await page.evaluate(() => localStorage.clear());
    
    // Step 3: Test login flow
    console.log('\n2️⃣ Testing Login Flow...');
    await page.goto('http://localhost:3000/login');
    
    // Verify demo credentials are visible and correct
    const demoCredentials = await page.textContent('.bg-light');
    expect(demoCredentials).toContain('<EMAIL>');
    expect(demoCredentials).toContain('AdminPass123!');
    
    // Fill and submit login
    await page.fill('input[name="identifier"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'AdminPass123!');
    await page.click('button[type="submit"]');
    
    // Wait for dashboard
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    console.log('✅ Login successful - redirected to dashboard');
    
    // Step 4: Verify authentication state
    console.log('\n3️⃣ Verifying Authentication State...');
    const authData = await page.evaluate(() => ({
      token: !!localStorage.getItem('token'),
      user: !!localStorage.getItem('user'),
      refreshToken: !!localStorage.getItem('refreshToken')
    }));
    
    expect(authData.token).toBeTruthy();
    expect(authData.user).toBeTruthy();
    expect(authData.refreshToken).toBeTruthy();
    console.log('✅ All authentication data present');
    
    // Step 5: Test navigation and dashboard access
    console.log('\n4️⃣ Testing Dashboard Access...');
    
    // Check for navigation elements
    const hasNavigation = await page.locator('nav, .navbar').count() > 0;
    expect(hasNavigation).toBeTruthy();
    console.log('✅ Navigation bar present');
    
    // Check for dashboard content
    const hasDashboardContent = await page.locator('h1, h2, .dashboard').count() > 0;
    expect(hasDashboardContent).toBeTruthy();
    console.log('✅ Dashboard content loaded');
    
    // Step 6: Test page refresh (persistence)
    console.log('\n5️⃣ Testing Session Persistence...');
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Should still be on dashboard
    expect(page.url()).toContain('/dashboard');
    console.log('✅ Session persists after page refresh');
    
    // Step 7: Test API calls with authentication
    console.log('\n6️⃣ Testing Authenticated API Access...');
    const token = await page.evaluate(() => localStorage.getItem('token'));
    
    const profileResponse = await request.get('http://localhost:8080/api/auth/profile', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    expect(profileResponse.ok()).toBeTruthy();
    const profileData = await profileResponse.json();
    expect(profileData.data.user.email).toBe('<EMAIL>');
    console.log('✅ Authenticated API calls working');
    
    console.log('\n🎉 FINAL VALIDATION COMPLETE!');
    console.log('✅ Backend: Healthy and responsive');
    console.log('✅ Frontend: Loading correctly');
    console.log('✅ Authentication: Working end-to-end');
    console.log('✅ Navigation: Functional');
    console.log('✅ Session: Persistent');
    console.log('✅ API: Authenticated calls working');
    console.log('\n🚀 APPLICATION IS FULLY FUNCTIONAL!');
  });
});
