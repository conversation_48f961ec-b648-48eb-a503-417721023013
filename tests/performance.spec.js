const { test, expect } = require('@playwright/test');

test.describe('Performance Tests', () => {
  test('should load the main page quickly', async ({ page }) => {
    const startTime = Date.now();
    await page.goto('/');
    await expect(page.locator('h1')).toBeVisible();
    const loadTime = Date.now() - startTime;
    
    console.log(`Page load time: ${loadTime}ms`);
    expect(loadTime).toBeLessThan(5000); // Should load within 5 seconds
  });

  test('should handle large number of todos efficiently', async ({ page }) => {
    await page.goto('/');
    
    // Create many todos quickly
    for (let i = 0; i < 20; i++) {
      await page.fill('input[placeholder*="Ajouter"]', `Performance Test Todo ${i}`);
      await page.click('button:has-text("Ajouter")');
    }

    // Measure search performance
    const startTime = Date.now();
    await page.fill('input[placeholder*="Rechercher"]', 'Performance Test');
    await page.waitForTimeout(100); // Allow for debouncing
    const searchTime = Date.now() - startTime;
    
    console.log(`Search time for 20 todos: ${searchTime}ms`);
    expect(searchTime).toBeLessThan(1000); // Search should be fast
    
    // Verify all todos are found
    const todoCount = await page.locator('.todo-item').count();
    expect(todoCount).toBe(20);
  });

  test('should handle rapid interactions', async ({ page }) => {
    await page.goto('/');
    
    // Create a todo
    await page.fill('input[placeholder*="Ajouter"]', 'Rapid Test Todo');
    await page.click('button:has-text("Ajouter")');
    
    const todoItem = page.locator('.todo-item').first();
    
    // Rapidly toggle completion status
    for (let i = 0; i < 5; i++) {
      await todoItem.locator('input[type="checkbox"]').click();
      await page.waitForTimeout(50);
    }
    
    // Should still be responsive
    await expect(todoItem).toBeVisible();
  });

  test('should measure Core Web Vitals', async ({ page }) => {
    await page.goto('/');
    
    // Measure Largest Contentful Paint (LCP)
    const lcp = await page.evaluate(() => {
      return new Promise((resolve) => {
        const observer = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          const lastEntry = entries[entries.length - 1];
          resolve(lastEntry.startTime);
        });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
        
        // Fallback timeout
        setTimeout(() => resolve(0), 5000);
      });
    });
    
    console.log(`Largest Contentful Paint: ${lcp}ms`);
    expect(lcp).toBeLessThan(2500); // Good LCP is under 2.5s
  });

  test('should handle network failures gracefully', async ({ page, context }) => {
    await page.goto('/');
    
    // Block network requests
    await context.route('**/api/**', route => route.abort());
    
    // Try to create a todo
    await page.fill('input[placeholder*="Ajouter"]', 'Network Failure Test');
    await page.click('button:has-text("Ajouter")');
    
    // Should show error message
    await expect(page.locator('.alert-danger')).toBeVisible();
  });

  test('should be accessible', async ({ page }) => {
    await page.goto('/');
    
    // Check for proper heading structure
    await expect(page.locator('h1')).toBeVisible();
    
    // Check for form labels and accessibility
    const addButton = page.locator('button:has-text("Ajouter")');
    await expect(addButton).toBeVisible();
    
    // Check keyboard navigation
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await expect(page.locator('input[placeholder*="Ajouter"]')).toBeFocused();
  });

  test('should work offline (service worker)', async ({ page, context }) => {
    await page.goto('/');
    await expect(page.locator('h1')).toBeVisible();
    
    // Simulate offline condition
    await context.setOffline(true);
    
    // Page should still be accessible
    await page.reload();
    
    // Basic content should still be visible (if service worker is implemented)
    // This test will pass even without service worker as React apps work offline for static content
    await expect(page.locator('h1')).toBeVisible();
  });
});