const { test, expect } = require('@playwright/test');

test.describe('FinOps Application - Manual Validation', () => {
  test('Complete user journey validation', async ({ page, request }) => {
    console.log('🚀 Starting comprehensive application validation...');
    
    // Step 1: Test Backend Health
    console.log('1️⃣ Testing backend health...');
    const healthResponse = await request.get('/health');
    expect(healthResponse.ok()).toBeTruthy();
    console.log('✅ Backend is healthy');
    
    // Step 2: Test Security Headers
    console.log('2️⃣ Testing security headers...');
    const headers = healthResponse.headers();
    expect(headers['x-content-type-options']).toBe('nosniff');
    expect(headers['x-frame-options']).toBe('DENY');
    expect(headers['x-xss-protection']).toBe('1; mode=block');
    console.log('✅ Security headers are present');
    
    // Step 3: Test Frontend Loading
    console.log('3️⃣ Testing frontend loading...');
    await page.goto('http://localhost:3000');
    await expect(page.locator('h1')).toContainText('FinOps');
    console.log('✅ Frontend loads correctly');
    
    // Step 4: Test User Registration (API)
    console.log('4️⃣ Testing user registration...');
    const timestamp = Date.now();
    const testUser = {
      username: `testuser${timestamp}`,
      email: `test${timestamp}@finops.com`,
      password: 'TestPass123!',
      firstName: 'Test',
      lastName: 'User'
    };
    
    const registerResponse = await request.post('/api/auth/register', {
      data: testUser
    });
    expect(registerResponse.ok()).toBeTruthy();
    console.log('✅ User registration works');
    
    // Step 5: Test User Login (API)
    console.log('5️⃣ Testing user login...');
    const loginResponse = await request.post('/api/auth/login', {
      data: {
        identifier: testUser.email,
        password: testUser.password
      }
    });
    expect(loginResponse.ok()).toBeTruthy();
    const loginData = await loginResponse.json();
    expect(loginData.success).toBeTruthy();
    expect(loginData.data.token).toBeDefined();
    console.log('✅ User login works');
    
    // Step 6: Test Authenticated API Access
    console.log('6️⃣ Testing authenticated API access...');
    const profileResponse = await request.get('/api/auth/profile', {
      headers: {
        'Authorization': `Bearer ${loginData.data.token}`
      }
    });
    expect(profileResponse.ok()).toBeTruthy();
    console.log('✅ Authenticated API access works');
    
    // Step 7: Test Frontend Login Flow
    console.log('7️⃣ Testing frontend login flow...');
    await page.goto('http://localhost:3000/login');
    await page.fill('input[name="identifier"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'AdminPass123!');
    await page.click('button[type="submit"]');
    
    // Wait for navigation to dashboard
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    await expect(page.locator('h1, h2, .dashboard-title')).toBeVisible();
    console.log('✅ Frontend login and navigation works');
    
    // Step 8: Test Dashboard Functionality
    console.log('8️⃣ Testing dashboard functionality...');
    await expect(page.locator('body')).toContainText('Dashboard');
    console.log('✅ Dashboard loads correctly');
    
    // Step 9: Test Navigation
    console.log('9️⃣ Testing navigation...');
    const navLinks = page.locator('nav a, .nav a, .navbar a');
    const linkCount = await navLinks.count();
    expect(linkCount).toBeGreaterThan(0);
    console.log(`✅ Navigation has ${linkCount} links`);
    
    // Step 10: Test Logout
    console.log('🔟 Testing logout...');
    const logoutButton = page.locator('button:has-text("Logout"), a:has-text("Logout"), .logout');
    if (await logoutButton.count() > 0) {
      await logoutButton.first().click();
      await page.waitForURL('**/login', { timeout: 5000 });
      console.log('✅ Logout works');
    } else {
      console.log('⚠️ Logout button not found, but login flow works');
    }
    
    console.log('🎉 All core functionality validated successfully!');
  });
  
  test('API endpoints validation', async ({ request }) => {
    console.log('🔍 Testing API endpoints...');
    
    // Test protected endpoints return 401 without auth
    const protectedEndpoints = [
      '/api/todos',
      '/api/finops/dashboard',
      '/api/finops/budgets',
      '/api/auth/profile'
    ];
    
    for (const endpoint of protectedEndpoints) {
      const response = await request.get(endpoint);
      expect([401, 404]).toContain(response.status());
      console.log(`✅ ${endpoint} properly protected`);
    }
    
    console.log('✅ API security validation complete');
  });
  
  test('Input validation and security', async ({ request }) => {
    console.log('🛡️ Testing input validation and security...');
    
    // Test XSS prevention in registration
    const xssPayload = '<script>alert("xss")</script>';
    const xssResponse = await request.post('/api/auth/register', {
      data: {
        username: xssPayload,
        email: '<EMAIL>',
        password: 'ValidPass123!',
        firstName: 'Test',
        lastName: 'User'
      }
    });
    
    // Should either reject (400) or sanitize the input
    expect([400, 409, 422]).toContain(xssResponse.status());
    console.log('✅ XSS prevention works');
    
    // Test SQL injection prevention
    const sqlPayload = "admin' OR 1=1 --";
    const sqlResponse = await request.post('/api/auth/login', {
      data: {
        identifier: sqlPayload,
        password: 'password'
      }
    });
    
    expect([400, 401, 422]).toContain(sqlResponse.status());
    console.log('✅ SQL injection prevention works');
    
    console.log('✅ Security validation complete');
  });
});
