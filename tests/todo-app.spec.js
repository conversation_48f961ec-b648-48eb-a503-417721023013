const { test, expect } = require('@playwright/test');

test.describe('Todo App - Tests Complets FinOps', () => {
  test('Page d\'accueil se charge correctement', async ({ page }) => {
    await page.goto('/');
    
    // Vérifier le titre
    await expect(page).toHaveTitle(/Todo App FinOps/);
    
    // Vérifier le contenu principal
    await expect(page.locator('h1')).toContainText('Todo App - Projet FinOps');
    
    // Vérifier la présence du container
    await expect(page.locator('.container')).toBeVisible();
    
    // Vérifier le message d'info backend
    await expect(page.locator('.alert-info')).toContainText('Backend connecté à');
  });

  test('Tests de performance et chargement', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    
    const loadTime = Date.now() - startTime;
    
    // Le chargement doit être rapide (< 3 secondes)
    expect(loadTime).toBeLessThan(3000);
    
    // Vérifier que tous les assets CSS/JS se chargent
    const cssLoaded = page.locator('link[rel="stylesheet"]');
    const jsLoaded = page.locator('script[src]');
    
    await expect(cssLoaded).toBeAttached();
    await expect(jsLoaded).toBeAttached();
  });

  test('Tests de sécurité - Headers HTTP', async ({ request }) => {
    const response = await request.get('/');
    
    // Vérifier les headers de sécurité
    const headers = response.headers();
    
    expect(headers['x-frame-options']).toBe('SAMEORIGIN');
    expect(headers['x-content-type-options']).toBe('nosniff');
    expect(headers['x-xss-protection']).toBe('1; mode=block');
  });

  test('Tests de responsivité', async ({ page }) => {
    await page.goto('/');
    
    // Test desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('.container')).toBeVisible();
    
    // Test tablet
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('.container')).toBeVisible();
    
    // Test mobile
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('.container')).toBeVisible();
  });

  test('Navigation et routing', async ({ page }) => {
    await page.goto('/');
    
    // Test page d'accueil
    await expect(page.locator('h1')).toBeVisible();
    
    // Test navigation vers une page inexistante (doit revenir à l'accueil)
    await page.goto('/page-inexistante');
    await expect(page.locator('h1')).toContainText('Todo App');
  });

  test('Tests d\'accessibilité', async ({ page }) => {
    await page.goto('/');
    
    // Vérifier la structure sémantique
    await expect(page.locator('h1')).toBeVisible();
    
    // Vérifier les contrastes (approximatif)
    const bgColor = await page.locator('body').evaluate(el => 
      getComputedStyle(el).backgroundColor
    );
    expect(bgColor).toBeTruthy();
    
    // Vérifier que le contenu est lisible
    const textContent = await page.locator('.container').textContent();
    expect(textContent).toContain('Todo App');
  });
});

test.describe('API Tests - Backend Integration', () => {
  test('API Root endpoint', async ({ request }) => {
    const response = await request.get('/api/');
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.message).toContain('API Todo');
    expect(data.version).toBe('1.1.0');
    expect(data.endpoints).toBeDefined();
  });

  test('API Health endpoint', async ({ request }) => {
    const response = await request.get('/api/health');
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.status).toBe('healthy');
    expect(data.uptime).toBeGreaterThan(0);
    expect(data.memory).toBeDefined();
  });

  test('API Todos endpoint - GET', async ({ request }) => {
    const response = await request.get('/api/todos');
    
    // Peut être 200 avec une liste vide ou 500 si DB pas prête
    expect([200, 500]).toContain(response.status());
    
    if (response.status() === 200) {
      const data = await response.json();
      expect(Array.isArray(data)).toBe(true);
    }
  });

  test('API Error handling', async ({ request }) => {
    const response = await request.get('/api/todos/invalid-id');
    
    // Doit gérer les erreurs gracieusement
    expect([400, 404, 500]).toContain(response.status());
  });
});

test.describe('FinOps Metrics Tests', () => {
  test('Performance Metrics', async ({ page }) => {
    await page.goto('/');
    
    // Mesurer les Web Vitals approximatifs
    const performanceMetrics = await page.evaluate(() => {
      return {
        loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
        domReady: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
        firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 0
      };
    });
    
    expect(performanceMetrics.loadTime).toBeLessThan(5000); // 5s max
    expect(performanceMetrics.domReady).toBeLessThan(3000);  // 3s max
    
    console.log('Performance Metrics:', performanceMetrics);
  });

  test('Resource Efficiency', async ({ page }) => {
    await page.goto('/');
    
    // Mesurer l'utilisation des ressources
    const resources = await page.evaluate(() => {
      const entries = performance.getEntriesByType('resource');
      return {
        totalResources: entries.length,
        totalSize: entries.reduce((total, entry) => total + (entry.transferSize || 0), 0),
        slowResources: entries.filter(entry => entry.duration > 1000).length
      };
    });
    
    expect(resources.totalResources).toBeLessThan(20); // Max 20 ressources
    expect(resources.slowResources).toBeLessThan(3);   // Max 3 ressources lentes
    
    console.log('Resource Metrics:', resources);
  });
});