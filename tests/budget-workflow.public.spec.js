const { test, expect } = require('@playwright/test');

test.describe('Budget Management Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('http://localhost:3000/login');
    await page.fill('input[name="identifier"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'AdminPass123!');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  });

  test('Complete budget management workflow', async ({ page }) => {
    console.log('💰 Testing complete budget workflow...');
    
    // Navigate to budget management
    await page.goto('http://localhost:3000/budgets');
    await page.waitForLoadState('networkidle');
    
    // Verify page loaded
    await expect(page.locator('h1')).toContainText('Budget Management');
    console.log('✅ Budget page loaded');
    
    // Create new budget
    await page.click('button:has-text("Create Budget")');
    await page.waitForSelector('form');
    
    console.log('📝 Filling budget form...');
    
    // Fill basic information
    await page.fill('input[value=""]', 'E2E Test Budget');
    await page.fill('input[type="number"]', '5000');
    await page.fill('textarea', 'Test budget for E2E testing');
    
    // Fill team information
    const teamInputs = page.locator('input[placeholder*="team"], input[placeholder*="Enter team"]');
    if (await teamInputs.count() > 0) {
      await teamInputs.first().fill('QA Team');
    }
    
    // Set dates
    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
    
    const dateInputs = page.locator('input[type="date"]');
    await dateInputs.first().fill(today.toISOString().split('T')[0]);
    await dateInputs.last().fill(nextMonth.toISOString().split('T')[0]);
    
    console.log('💾 Submitting budget...');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Wait for response
    await page.waitForTimeout(3000);
    
    // Check for success or error
    const successAlert = page.locator('.alert-success');
    const errorAlert = page.locator('.alert-danger');
    
    if (await successAlert.count() > 0) {
      console.log('✅ Budget created successfully');
      await expect(successAlert).toContainText('successfully');
      
      // Verify budget appears in list
      await page.waitForTimeout(2000);
      const budgetCard = page.locator('text=E2E Test Budget');
      if (await budgetCard.count() > 0) {
        console.log('✅ Budget appears in list');
        
        // Try to edit budget
        const dropdownButton = page.locator('.dropdown-toggle').first();
        if (await dropdownButton.count() > 0) {
          await dropdownButton.click();
          await page.waitForTimeout(500);
          
          const editButton = page.locator('text=Edit');
          if (await editButton.count() > 0) {
            await editButton.click();
            await page.waitForTimeout(1000);
            
            // Update budget name
            const nameInput = page.locator('input[value*="E2E Test Budget"]');
            if (await nameInput.count() > 0) {
              await nameInput.fill('E2E Test Budget Updated');
              await page.click('button:has-text("Update")');
              await page.waitForTimeout(2000);
              console.log('✅ Budget updated');
            }
          }
        }
        
        // Try to delete budget
        const deleteDropdown = page.locator('.dropdown-toggle').first();
        if (await deleteDropdown.count() > 0) {
          await deleteDropdown.click();
          await page.waitForTimeout(500);
          
          const deleteButton = page.locator('text=Delete');
          if (await deleteButton.count() > 0) {
            await deleteButton.click();
            await page.waitForTimeout(1000);
            
            // Confirm deletion in modal
            const confirmButton = page.locator('button:has-text("Delete Budget")');
            if (await confirmButton.count() > 0) {
              await confirmButton.click();
              await page.waitForTimeout(2000);
              console.log('✅ Budget deleted');
            }
          }
        }
      }
    } else if (await errorAlert.count() > 0) {
      const errorText = await errorAlert.textContent();
      console.log('❌ Budget creation failed:', errorText);
    } else {
      console.log('⚠️ No clear success/error message found');
    }
    
    console.log('🎉 Budget workflow test completed');
  });

  test('Dashboard integration test', async ({ page }) => {
    console.log('🏠 Testing Dashboard integration...');
    
    await page.goto('http://localhost:3000/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Verify dashboard elements
    await expect(page.locator('h1')).toContainText('FinOps Dashboard');
    
    // Check for budget section
    const budgetSection = page.locator('text=Active Budgets');
    await expect(budgetSection).toBeVisible();
    
    // Check for summary cards
    const summaryCards = page.locator('.card');
    await expect(summaryCards).toHaveCountGreaterThan(0);
    
    // Test refresh functionality
    const refreshButton = page.locator('button:has-text("Refresh")');
    if (await refreshButton.count() > 0) {
      await refreshButton.click();
      await page.waitForTimeout(1000);
      console.log('✅ Refresh button works');
    }
    
    console.log('✅ Dashboard integration verified');
  });

  test('Cost Analysis page functionality', async ({ page }) => {
    console.log('📊 Testing Cost Analysis page...');
    
    await page.goto('http://localhost:3000/cost-analysis');
    await page.waitForLoadState('networkidle');
    
    // Verify page loads
    await expect(page.locator('h1')).toContainText('Cost Analysis');
    
    // Check for filters section
    const filtersCard = page.locator('text=Filters & Options');
    await expect(filtersCard).toBeVisible();
    
    // Test filter interactions
    const groupBySelect = page.locator('select').first();
    if (await groupBySelect.count() > 0) {
      await groupBySelect.selectOption('week');
      await page.waitForTimeout(1000);
      console.log('✅ Filter interaction works');
    }
    
    // Check for export buttons
    const exportButtons = page.locator('button:has-text("Export")');
    await expect(exportButtons).toHaveCountGreaterThan(0);
    
    console.log('✅ Cost Analysis functionality verified');
  });

  test('Navigation between pages', async ({ page }) => {
    console.log('🧭 Testing navigation...');
    
    const pages = [
      { name: 'Dashboard', url: '/dashboard' },
      { name: 'Cost Analysis', url: '/cost-analysis' },
      { name: 'Budgets', url: '/budgets' },
      { name: 'Reports', url: '/reports' }
    ];
    
    for (const pageInfo of pages) {
      await page.goto(`http://localhost:3000${pageInfo.url}`);
      await page.waitForLoadState('networkidle');
      
      // Verify URL
      expect(page.url()).toContain(pageInfo.url);
      
      // Verify page content loads
      const heading = page.locator('h1');
      await expect(heading).toBeVisible();
      
      console.log(`✅ ${pageInfo.name} page accessible`);
    }
    
    console.log('✅ Navigation test completed');
  });

  test('Authentication persistence', async ({ page }) => {
    console.log('🔐 Testing authentication persistence...');
    
    // Go to dashboard
    await page.goto('http://localhost:3000/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Verify we're on dashboard (authenticated)
    await expect(page).toHaveURL(/dashboard/);
    
    // Check localStorage has auth data
    const authData = await page.evaluate(() => {
      return {
        token: !!localStorage.getItem('token'),
        user: !!localStorage.getItem('user')
      };
    });
    
    expect(authData.token).toBeTruthy();
    expect(authData.user).toBeTruthy();
    
    // Refresh page and verify still authenticated
    await page.reload();
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL(/dashboard/);
    
    console.log('✅ Authentication persistence verified');
  });
});
