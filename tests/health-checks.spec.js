const { test, expect } = require('@playwright/test');

test.describe('Health Checks - Services FinOps', () => {
  test('Frontend Health Check', async ({ request }) => {
    const response = await request.get('/health');
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.status).toBe('healthy');
    expect(data.service).toBe('frontend');
    expect(data.version).toBe('1.1.0');
  });

  test('Frontend Readiness Check', async ({ request }) => {
    const response = await request.get('/ready');
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.status).toBe('ready');
    expect(data.service).toBe('frontend');
  });

  test('Frontend Liveness Check', async ({ request }) => {
    const response = await request.get('/live');
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.status).toBe('alive');
    expect(data.service).toBe('frontend');
  });

  test('Backend Health Check via API Proxy', async ({ request }) => {
    const response = await request.get('/api/');
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.message).toContain('API Todo - Projet FinOps');
    expect(data.version).toBe('1.1.0');
    expect(data.endpoints).toBeDefined();
  });

  test('Backend Health Check Direct', async ({ request }) => {
    const response = await request.get('/api/health');
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.status).toBe('healthy');
    expect(data.database).toBe('connected');
    expect(data.ready).toBe(true);
  });
}); 