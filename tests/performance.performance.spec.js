const { test, expect } = require('@playwright/test');

test.describe('Performance Testing', () => {
  let authToken;
  
  test.beforeAll(async ({ request }) => {
    // Get authentication token
    const loginResponse = await request.post('/auth/login', {
      data: {
        identifier: '<EMAIL>',
        password: 'AdminPass123!'
      }
    });
    
    expect(loginResponse.ok()).toBeTruthy();
    const loginData = await loginResponse.json();
    authToken = loginData.data.token;
  });
  
  test.describe('API Response Times', () => {
    test('dashboard should load within acceptable time', async ({ request }) => {
      const startTime = Date.now();
      
      const response = await request.get('/finops/dashboard', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.ok()).toBeTruthy();
      expect(responseTime).toBeLessThan(2000); // Should load within 2 seconds
      
      console.log(`Dashboard response time: ${responseTime}ms`);
    });
    
    test('cost analysis should be performant', async ({ request }) => {
      const startTime = Date.now();
      
      const response = await request.get('/finops/cost-analysis', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.ok()).toBeTruthy();
      expect(responseTime).toBeLessThan(3000); // Should load within 3 seconds
      
      console.log(`Cost analysis response time: ${responseTime}ms`);
    });
    
    test('budget list should load quickly', async ({ request }) => {
      const startTime = Date.now();
      
      const response = await request.get('/finops/budgets', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.ok()).toBeTruthy();
      expect(responseTime).toBeLessThan(1000); // Should load within 1 second
      
      console.log(`Budget list response time: ${responseTime}ms`);
    });
    
    test('optimization recommendations should be reasonable', async ({ request }) => {
      const startTime = Date.now();
      
      const response = await request.get('/finops/optimization-recommendations', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.ok()).toBeTruthy();
      expect(responseTime).toBeLessThan(5000); // Should load within 5 seconds
      
      console.log(`Optimization recommendations response time: ${responseTime}ms`);
    });
  });
  
  test.describe('Concurrent Load Testing', () => {
    test('should handle multiple concurrent dashboard requests', async ({ request }) => {
      const concurrentRequests = 10;
      const promises = [];
      const startTime = Date.now();
      
      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          request.get('/finops/dashboard', {
            headers: {
              'Authorization': `Bearer ${authToken}`
            }
          })
        );
      }
      
      const responses = await Promise.all(promises);
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      // All requests should succeed
      for (const response of responses) {
        expect(response.ok()).toBeTruthy();
      }
      
      // Average response time should be reasonable
      const avgResponseTime = totalTime / concurrentRequests;
      expect(avgResponseTime).toBeLessThan(3000);
      
      console.log(`${concurrentRequests} concurrent requests completed in ${totalTime}ms`);
      console.log(`Average response time: ${avgResponseTime}ms`);
    });
    
    test('should handle concurrent budget operations', async ({ request }) => {
      const concurrentRequests = 5;
      const promises = [];
      
      // Create multiple budgets concurrently
      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          request.post('/finops/budgets', {
            headers: {
              'Authorization': `Bearer ${authToken}`
            },
            data: {
              name: `Concurrent Test Budget ${i}`,
              description: `Budget ${i} for concurrent testing`,
              scope: {
                type: 'team',
                filters: {
                  teams: [`test-team-${i}`]
                }
              },
              period: {
                type: 'monthly',
                startDate: new Date().toISOString(),
                endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
              },
              amounts: {
                total: 1000 + i * 100,
                currency: 'EUR'
              }
            }
          })
        );
      }
      
      const responses = await Promise.all(promises);
      
      // All requests should succeed
      for (const response of responses) {
        expect(response.status()).toBe(201);
      }
      
      console.log(`${concurrentRequests} concurrent budget creations completed successfully`);
    });
  });
  
  test.describe('Memory and Resource Usage', () => {
    test('should not cause memory leaks with repeated requests', async ({ request }) => {
      const iterations = 50;
      const responseTimes = [];
      
      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        
        const response = await request.get('/finops/dashboard', {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        responseTimes.push(responseTime);
        
        expect(response.ok()).toBeTruthy();
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 10));
      }
      
      // Calculate statistics
      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const maxResponseTime = Math.max(...responseTimes);
      const minResponseTime = Math.min(...responseTimes);
      
      console.log(`${iterations} iterations completed`);
      console.log(`Average response time: ${avgResponseTime.toFixed(2)}ms`);
      console.log(`Min response time: ${minResponseTime}ms`);
      console.log(`Max response time: ${maxResponseTime}ms`);
      
      // Response times should remain consistent (no significant degradation)
      expect(maxResponseTime).toBeLessThan(avgResponseTime * 3);
    });
  });
  
  test.describe('Database Performance', () => {
    test('should handle large dataset queries efficiently', async ({ request }) => {
      // Test cost analysis with date range (potentially large dataset)
      const startTime = Date.now();
      
      const response = await request.get('/finops/cost-analysis?startDate=2025-01-01&endDate=2025-12-31', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.ok()).toBeTruthy();
      expect(responseTime).toBeLessThan(5000); // Should handle large queries within 5 seconds
      
      console.log(`Large dataset query response time: ${responseTime}ms`);
    });
    
    test('should efficiently generate reports', async ({ request }) => {
      const startTime = Date.now();
      
      const response = await request.get('/finops/cost-report?format=csv', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.ok()).toBeTruthy();
      expect(responseTime).toBeLessThan(10000); // Report generation within 10 seconds
      
      console.log(`Report generation time: ${responseTime}ms`);
    });
  });
  
  test.describe('Frontend Performance', () => {
    test('should load dashboard page quickly', async ({ page }) => {
      // Navigate to dashboard
      const startTime = Date.now();
      await page.goto('/finops/dashboard');
      
      // Wait for main content to load
      await page.waitForSelector('[data-testid="dashboard-content"], .dashboard-main, .cost-metric', { timeout: 10000 });
      
      const endTime = Date.now();
      const loadTime = endTime - startTime;
      
      expect(loadTime).toBeLessThan(5000); // Page should load within 5 seconds
      
      console.log(`Dashboard page load time: ${loadTime}ms`);
    });
    
    test('should handle rapid navigation', async ({ page }) => {
      const pages = [
        '/finops/dashboard',
        '/finops/budgets',
        '/finops/cost-analysis',
        '/todos'
      ];
      
      const navigationTimes = [];
      
      for (const pagePath of pages) {
        const startTime = Date.now();
        await page.goto(pagePath);
        
        // Wait for page to be ready
        await page.waitForLoadState('networkidle');
        
        const endTime = Date.now();
        const navigationTime = endTime - startTime;
        navigationTimes.push(navigationTime);
        
        console.log(`Navigation to ${pagePath}: ${navigationTime}ms`);
      }
      
      const avgNavigationTime = navigationTimes.reduce((a, b) => a + b, 0) / navigationTimes.length;
      expect(avgNavigationTime).toBeLessThan(3000);
      
      console.log(`Average navigation time: ${avgNavigationTime.toFixed(2)}ms`);
    });
  });
  
  test.describe('Scalability Testing', () => {
    test('should maintain performance with multiple users', async ({ request }) => {
      // Simulate multiple user sessions
      const userTokens = [];
      
      // Create multiple user sessions
      for (let i = 0; i < 3; i++) {
        const loginResponse = await request.post('/auth/login', {
          data: {
            identifier: '<EMAIL>',
            password: 'AdminPass123!'
          }
        });
        
        if (loginResponse.ok()) {
          const loginData = await loginResponse.json();
          userTokens.push(loginData.data.token);
        }
      }
      
      // Perform concurrent operations with different users
      const promises = [];
      
      for (const token of userTokens) {
        promises.push(
          request.get('/finops/dashboard', {
            headers: { 'Authorization': `Bearer ${token}` }
          })
        );
        promises.push(
          request.get('/finops/budgets', {
            headers: { 'Authorization': `Bearer ${token}` }
          })
        );
      }
      
      const startTime = Date.now();
      const responses = await Promise.all(promises);
      const endTime = Date.now();
      
      // All requests should succeed
      for (const response of responses) {
        expect(response.ok()).toBeTruthy();
      }
      
      const totalTime = endTime - startTime;
      console.log(`Multi-user concurrent operations completed in ${totalTime}ms`);
    });
  });
});
