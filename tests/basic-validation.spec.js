const { test, expect } = require('@playwright/test');

test.describe('Validation Basique - Application FinOps', () => {
  test('L\'application se charge correctement', async ({ page }) => {
    await page.goto('/');
    
    // Vérifier que la page se charge
    await expect(page).toHaveTitle(/React App/);
    
    // Vérifier la présence du contenu
    await expect(page.locator('body')).toBeVisible();
    
    // Log du contenu pour debug
    const content = await page.textContent('body');
    console.log('Contenu de la page:', content?.substring(0, 200));
  });

  test('Health Check Frontend fonctionne', async ({ request }) => {
    const response = await request.get('/health');
    
    expect(response.status()).toBe(200);
    
    const contentType = response.headers()['content-type'];
    
    if (contentType?.includes('application/json')) {
      const data = await response.json();
      expect(data.service).toBe('frontend');
      expect(data.status).toBe('healthy');
    } else {
      // Si c'est du HTML, vérifier au moins que ça répond
      const text = await response.text();
      expect(text).toContain('healthy');
    }
  });

  test('Ready Check Frontend fonctionne', async ({ request }) => {
    const response = await request.get('/ready');
    
    expect(response.status()).toBe(200);
    
    const contentType = response.headers()['content-type'];
    
    if (contentType?.includes('application/json')) {
      const data = await response.json();
      expect(data.service).toBe('frontend');
    } else {
      // Si c'est du HTML, vérifier la réponse
      const text = await response.text();
      expect(text).toContain('ready');
    }
  });

  test('Live Check Frontend fonctionne', async ({ request }) => {
    const response = await request.get('/live');
    
    expect(response.status()).toBe(200);
    
    const contentType = response.headers()['content-type'];
    
    if (contentType?.includes('application/json')) {
      const data = await response.json();
      expect(data.service).toBe('frontend');
    } else {
      const text = await response.text();
      expect(text).toContain('alive');
    }
  });

  test('API Root accessible', async ({ request }) => {
    const response = await request.get('/api/');
    
    // Accepter 200 (OK) ou 500 (si MongoDB pas prêt)
    expect([200, 500]).toContain(response.status());
    
    const contentType = response.headers()['content-type'];
    
    if (contentType?.includes('application/json')) {
      const data = await response.json();
      
      if (response.status() === 200) {
        expect(data.message).toContain('API');
      } else {
        expect(data.error).toBeDefined();
      }
    }
  });

  test('API Health Check', async ({ request }) => {
    const response = await request.get('/api/health');
    
    // Accepter 200 ou 500 selon l'état de MongoDB
    expect([200, 500]).toContain(response.status());
    
    const contentType = response.headers()['content-type'];
    
    if (contentType?.includes('application/json')) {
      const data = await response.json();
      
      if (response.status() === 200) {
        expect(data.status).toBe('healthy');
      } else {
        expect(data.error).toBeDefined();
      }
    }
  });

  test('Performance - Page Load Time', async ({ page }) => {
    const startTime = Date.now();
    await page.goto('/');
    const loadTime = Date.now() - startTime;
    
    // La page doit se charger en moins de 5 secondes
    expect(loadTime).toBeLessThan(5000);
    
    console.log(`Temps de chargement: ${loadTime}ms`);
  });

  test('Sécurité - Headers de base', async ({ request }) => {
    const response = await request.get('/');
    
    const headers = response.headers();
    
    // Vérifier au moins que les headers sont présents
    expect(headers['content-type']).toBeDefined();
    expect(headers['server']).toBeDefined();
    
    console.log('Headers de sécurité:', {
      'content-type': headers['content-type'],
      'server': headers['server'],
      'x-powered-by': headers['x-powered-by'] || 'non défini'
    });
  });

  test('Responsivité - Mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    // Vérifier que la page se charge sur mobile
    await expect(page.locator('body')).toBeVisible();
    
    // Vérifier la taille du viewport
    const viewportSize = page.viewportSize();
    expect(viewportSize.width).toBe(375);
    expect(viewportSize.height).toBe(667);
  });

  test('Responsivité - Desktop', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.goto('/');
    
    // Vérifier que la page se charge sur desktop
    await expect(page.locator('body')).toBeVisible();
    
    const viewportSize = page.viewportSize();
    expect(viewportSize.width).toBe(1920);
    expect(viewportSize.height).toBe(1080);
  });

  test('Métriques FinOps - Ressources', async ({ page }) => {
    await page.goto('/');
    
    // Mesurer les métriques de performance
    const metrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0];
      const resources = performance.getEntriesByType('resource');
      
      return {
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        resourceCount: resources.length,
        totalSize: resources.reduce((total, resource) => total + (resource.transferSize || 0), 0),
        domReady: navigation.domContentLoadedEventEnd - navigation.navigationStart
      };
    });
    
    // Vérifications des métriques
    expect(metrics.loadTime).toBeLessThan(3000); // < 3s
    expect(metrics.resourceCount).toBeLessThan(50); // < 50 ressources
    expect(metrics.domReady).toBeLessThan(2000); // < 2s
    
    console.log('Métriques FinOps:', metrics);
  });
}); 