const { test, expect } = require('@playwright/test');

test.describe('API Tests', () => {
  const baseURL = 'http://localhost:8080/api';

  test('should get health status', async ({ request }) => {
    const response = await request.get('http://localhost:8080/health');
    expect(response.ok()).toBeTruthy();
    
    const health = await response.json();
    expect(health).toHaveProperty('status', 'healthy');
    expect(health).toHaveProperty('uptime');
    expect(health).toHaveProperty('timestamp');
    expect(health).toHaveProperty('memory');
  });

  test('should create a todo via API', async ({ request }) => {
    const newTodo = {
      title: 'API Test Todo',
      description: 'Created via API test'
    };

    const response = await request.post(`${baseURL}/todos`, {
      data: newTodo
    });

    expect(response.ok()).toBeTruthy();
    const todo = await response.json();
    expect(todo).toHaveProperty('_id');
    expect(todo).toHaveProperty('title', newTodo.title);
    expect(todo).toHaveProperty('description', newTodo.description);
    expect(todo).toHaveProperty('completed', false);
  });

  test('should get all todos via API', async ({ request }) => {
    const response = await request.get(`${baseURL}/todos`);
    expect(response.ok()).toBeTruthy();
    
    const todos = await response.json();
    expect(Array.isArray(todos)).toBeTruthy();
  });

  test('should update a todo via API', async ({ request }) => {
    // Create a todo first
    const newTodo = {
      title: 'Update Test Todo',
      description: 'To be updated'
    };

    const createResponse = await request.post(`${baseURL}/todos`, {
      data: newTodo
    });
    const createdTodo = await createResponse.json();

    // Update the todo
    const updateData = {
      title: 'Updated Todo Title',
      completed: true
    };

    const updateResponse = await request.put(`${baseURL}/todos/${createdTodo._id}`, {
      data: updateData
    });

    expect(updateResponse.ok()).toBeTruthy();
    const updatedTodo = await updateResponse.json();
    expect(updatedTodo).toHaveProperty('title', updateData.title);
    expect(updatedTodo).toHaveProperty('completed', true);
  });

  test('should delete a todo via API', async ({ request }) => {
    // Create a todo first
    const newTodo = {
      title: 'Delete Test Todo',
      description: 'To be deleted'
    };

    const createResponse = await request.post(`${baseURL}/todos`, {
      data: newTodo
    });
    const createdTodo = await createResponse.json();

    // Delete the todo
    const deleteResponse = await request.delete(`${baseURL}/todos/${createdTodo._id}`);
    expect(deleteResponse.ok()).toBeTruthy();

    // Verify it's deleted
    const getResponse = await request.get(`${baseURL}/todos/${createdTodo._id}`);
    expect(getResponse.status()).toBe(404);
  });

  test('should get todo statistics via API', async ({ request }) => {
    const response = await request.get(`${baseURL}/todos/stats/summary`);
    expect(response.ok()).toBeTruthy();
    
    const stats = await response.json();
    expect(stats).toHaveProperty('total');
    expect(stats).toHaveProperty('completed');
    expect(stats).toHaveProperty('pending');
    expect(stats).toHaveProperty('completionRate');
  });

  test('should search todos via API', async ({ request }) => {
    // Create a searchable todo
    const searchTodo = {
      title: 'Searchable Todo ' + Date.now(),
      description: 'This is searchable'
    };

    await request.post(`${baseURL}/todos`, { data: searchTodo });

    // Search for it
    const response = await request.get(`${baseURL}/todos/search/Searchable`);
    expect(response.ok()).toBeTruthy();
    
    const results = await response.json();
    expect(Array.isArray(results)).toBeTruthy();
    expect(results.some(todo => todo.title.includes('Searchable'))).toBeTruthy();
  });

  test('should handle validation errors', async ({ request }) => {
    // Try to create todo without title
    const invalidTodo = {
      description: 'No title provided'
    };

    const response = await request.post(`${baseURL}/todos`, {
      data: invalidTodo
    });

    expect(response.status()).toBe(400);
    const error = await response.json();
    expect(error).toHaveProperty('success', false);
  });

  test('should handle invalid todo ID', async ({ request }) => {
    const response = await request.get(`${baseURL}/todos/invalid-id`);
    expect(response.status()).toBe(400);
    
    const error = await response.json();
    expect(error).toHaveProperty('success', false);
    expect(error).toHaveProperty('message', 'Invalid ID format');
  });

  test('should handle rate limiting', async ({ request }) => {
    // Make multiple rapid requests
    const requests = Array(10).fill().map(() => 
      request.get(`${baseURL}/todos`)
    );

    const responses = await Promise.all(requests);
    
    // All should succeed under normal rate limits
    responses.forEach(response => {
      expect(response.ok()).toBeTruthy();
    });
  });
});