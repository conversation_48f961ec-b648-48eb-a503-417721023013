version: '3.8'

services:
  # Frontend React
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - backend
    environment:
      - REACT_APP_API_URL=http://localhost:8080/api
    networks:
      - app-network

  # Backend Node.js
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    depends_on:
      - mongo
    environment:
      - NODE_ENV=production
      - PORT=8080
      - DB_URL=mongodb://mongo:27017/todoapp
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Base de données MongoDB
  mongo:
    image: mongo:5.0-focal
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_DATABASE=todoapp
    volumes:
      - mongo_data:/data/db
    networks:
      - app-network
    healthcheck:
      test: ["C<PERSON>", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  mongo_data:

networks:
  app-network:
    driver: bridge
