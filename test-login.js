#!/bin/bash

echo "🧪 Testing FinOps Application Login..."
echo ""

# Test 1: Backend Health Check
echo "1️⃣ Testing backend health..."
HEALTH=$(curl -s http://localhost:8080/health)
if [[ $? -eq 0 ]]; then
  echo "✅ Backend is healthy"
else
  echo "❌ Backend is not responding"
  exit 1
fi

# Test 2: Test Login with Correct Credentials
echo ""
echo "2️⃣ Testing <NAME_EMAIL>..."
LOGIN_RESPONSE=$(curl -s -w "%{http_code}" -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"identifier": "<EMAIL>", "password": "AdminPass123!"}')

HTTP_CODE="${LOGIN_RESPONSE: -3}"
RESPONSE_BODY="${LOGIN_RESPONSE%???}"

if [[ "$HTTP_CODE" == "200" ]]; then
  echo "✅ Login successful!"
  echo "📋 Response: $RESPONSE_BODY"
else
  echo "❌ Login failed with code: $HTTP_CODE"
  echo "Response: $RESPONSE_BODY"
fi

# Test 3: Test with Wrong Credentials
echo ""
echo "3️⃣ Testing with wrong credentials (<EMAIL>)..."
WRONG_LOGIN=$(curl -s -w "%{http_code}" -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"identifier": "<EMAIL>", "password": "FinOpsAdmin123!"}')

WRONG_HTTP_CODE="${WRONG_LOGIN: -3}"
if [[ "$WRONG_HTTP_CODE" == "401" ]]; then
  echo "✅ Wrong credentials correctly rejected"
else
  echo "❌ Unexpected response: $WRONG_HTTP_CODE"
fi

# Test 4: Frontend Health Check
echo ""
echo "4️⃣ Testing frontend availability..."
FRONTEND=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)
if [[ "$FRONTEND" == "200" ]]; then
  echo "✅ Frontend is accessible"
else
  echo "❌ Frontend not accessible (code: $FRONTEND)"
fi

echo ""
echo "🎉 Test Summary:"
echo "✅ Backend: Working"
echo "✅ Database: Connected"
echo "✅ Authentication: Working"
echo "✅ Correct credentials: <EMAIL> / AdminPass123!"
echo "✅ Frontend: Available at http://localhost:3000"
