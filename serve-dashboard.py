#!/usr/bin/env python3

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse

PORT = 4000

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        # Serve dashboard for root and /dashboard paths
        if parsed_path.path in ['/', '/dashboard']:
            self.path = '/finops-dashboard.html'
        
        # Health check endpoint
        elif parsed_path.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            health_data = {
                "status": "healthy",
                "service": "FinOps Dashboard Server",
                "port": PORT,
                "timestamp": "2025-07-14T14:00:00Z"
            }
            self.wfile.write(str(health_data).encode())
            return
        
        super().do_GET()

def main():
    # Change to the directory containing this script
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        with socketserver.TCPServer(("", PORT), CORSHTTPRequestHandler) as httpd:
            print(f"🎯 FinOps Dashboard Server running at http://localhost:{PORT}")
            print(f"📊 Dashboard available at: http://localhost:{PORT}/dashboard")
            print(f"🔗 Backend API: http://localhost:8080")
            print(f"✅ CORS enabled for all origins")
            print(f"🚀 Server started successfully on port {PORT}")
            httpd.serve_forever()
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {PORT} is already in use. Please choose a different port.")
        else:
            print(f"❌ Server error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        sys.exit(0)

if __name__ == "__main__":
    main()
