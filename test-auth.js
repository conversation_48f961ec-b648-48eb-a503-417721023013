#!/usr/bin/env node

const https = require('http');

console.log('🔐 Testing FinOps Authentication...');

// Test login
const loginData = JSON.stringify({
  identifier: '<EMAIL>',
  password: 'FinOpsAdmin123!'
});

const options = {
  hostname: 'localhost',
  port: 8080,
  path: '/api/auth/login',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(loginData)
  }
};

const req = https.request(options, (res) => {
  console.log(`✅ Login Status: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      if (response.token) {
        console.log('🎯 Authentication successful!');
        console.log('📊 User:', response.user.username);
        console.log('🔑 Token received:', response.token.substring(0, 20) + '...');
        
        // Test dashboard access with token
        testDashboard(response.token);
      } else {
        console.log('❌ Authentication failed:', response.message);
      }
    } catch (error) {
      console.log('❌ Error parsing response:', error.message);
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.log('❌ Request error:', error.message);
});

req.write(loginData);
req.end();

function testDashboard(token) {
  console.log('\n📊 Testing Dashboard Access...');
  
  const dashboardOptions = {
    hostname: 'localhost',
    port: 8080,
    path: '/api/finops/dashboard',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  };

  const dashReq = https.request(dashboardOptions, (res) => {
    console.log(`✅ Dashboard Status: ${res.statusCode}`);
    
    let dashData = '';
    res.on('data', (chunk) => {
      dashData += chunk;
    });
    
    res.on('end', () => {
      try {
        const dashResponse = JSON.parse(dashData);
        console.log('🎯 Dashboard data received!');
        console.log('💰 Total Cost:', dashResponse.summary?.totalCost || 'N/A');
        console.log('📈 Cost Trends:', dashResponse.costTrends?.length || 0, 'entries');
        console.log('👥 Teams:', dashResponse.teamBreakdown?.length || 0, 'teams');
        console.log('🏆 Top Resources:', dashResponse.topResources?.length || 0, 'resources');
        console.log('\n✅ FinOps Authentication & Dashboard: WORKING!');
      } catch (error) {
        console.log('❌ Error parsing dashboard response:', error.message);
      }
    });
  });

  dashReq.on('error', (error) => {
    console.log('❌ Dashboard request error:', error.message);
  });

  dashReq.end();
}
