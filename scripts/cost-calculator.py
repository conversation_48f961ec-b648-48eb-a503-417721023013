#!/usr/bin/env python3

"""
Calculateur de coûts FinOps pour Kubernetes
Auteur: DevOps Team
Date: 2024
"""

import json
import subprocess
import sys
from datetime import datetime
from typing import Dict, List, Tuple

# Prix simulés (en euros)
PRICING = {
    'cpu_per_vcpu_hour': 0.02,
    'memory_per_gb_hour': 0.01,
    'storage_per_gb_month': 0.03,
    'hours_per_month': 720
}

def run_kubectl(cmd: str) -> str:
    """Exécuter une commande kubectl et retourner la sortie"""
    try:
        result = subprocess.run(
            f"kubectl {cmd}",
            shell=True,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Erreur kubectl: {e}")
        return ""

def parse_cpu(cpu_str: str) -> float:
    """Convertir une valeur CPU en vCPU (float)"""
    if not cpu_str:
        return 0.0
    
    cpu_str = cpu_str.strip()
    if cpu_str.endswith('m'):
        return float(cpu_str[:-1]) / 1000
    elif cpu_str.endswith('n'):
        return float(cpu_str[:-1]) / 1000000000
    else:
        return float(cpu_str)

def parse_memory(memory_str: str) -> float:
    """Convertir une valeur mémoire en GB (float)"""
    if not memory_str:
        return 0.0
    
    memory_str = memory_str.strip()
    if memory_str.endswith('Ki'):
        return float(memory_str[:-2]) / 1024 / 1024
    elif memory_str.endswith('Mi'):
        return float(memory_str[:-2]) / 1024
    elif memory_str.endswith('Gi'):
        return float(memory_str[:-2])
    elif memory_str.endswith('Ti'):
        return float(memory_str[:-2]) * 1024
    else:
        return float(memory_str) / 1024 / 1024 / 1024

def get_pod_resources() -> List[Dict]:
    """Récupérer les ressources des pods"""
    pods_data = []
    
    # Obtenir la liste des pods
    pods_output = run_kubectl("get pods -n finops-app -o jsonpath='{.items[*].metadata.name}'")
    if not pods_output:
        return []
    
    pod_names = pods_output.strip().split()
    
    for pod_name in pod_names:
        # Obtenir les ressources demandées
        requests_output = run_kubectl(
            f"get pod {pod_name} -n finops-app -o jsonpath='{{.spec.containers[0].resources.requests}}'"
        )
        
        # Obtenir les ressources limites
        limits_output = run_kubectl(
            f"get pod {pod_name} -n finops-app -o jsonpath='{{.spec.containers[0].resources.limits}}'"
        )
        
        # Obtenir l'utilisation actuelle
        usage_output = run_kubectl(f"top pod {pod_name} -n finops-app --no-headers")
        
        # Parser les données
        requests = json.loads(requests_output) if requests_output else {}
        limits = json.loads(limits_output) if limits_output else {}
        
        cpu_usage = 0.0
        memory_usage = 0.0
        if usage_output:
            parts = usage_output.strip().split()
            if len(parts) >= 3:
                cpu_usage = parse_cpu(parts[1])
                memory_usage = parse_memory(parts[2])
        
        pod_data = {
            'name': pod_name,
            'app': pod_name.split('-')[0],
            'cpu_request': parse_cpu(requests.get('cpu', '0')),
            'memory_request': parse_memory(requests.get('memory', '0')),
            'cpu_limit': parse_cpu(limits.get('cpu', '0')),
            'memory_limit': parse_memory(limits.get('memory', '0')),
            'cpu_usage': cpu_usage,
            'memory_usage': memory_usage
        }
        
        pods_data.append(pod_data)
    
    return pods_data

def calculate_costs(pods_data: List[Dict]) -> Tuple[Dict, Dict]:
    """Calculer les coûts pour chaque pod"""
    costs = {}
    usage_stats = {}
    
    for pod in pods_data:
        # Coût basé sur les requests
        cpu_cost_hour = pod['cpu_request'] * PRICING['cpu_per_vcpu_hour']
        memory_cost_hour = pod['memory_request'] * PRICING['memory_per_gb_hour']
        total_cost_hour = cpu_cost_hour + memory_cost_hour
        total_cost_month = total_cost_hour * PRICING['hours_per_month']
        
        # Statistiques d'utilisation
        cpu_usage_percent = (pod['cpu_usage'] / pod['cpu_request'] * 100) if pod['cpu_request'] > 0 else 0
        memory_usage_percent = (pod['memory_usage'] / pod['memory_request'] * 100) if pod['memory_request'] > 0 else 0
        
        costs[pod['name']] = {
            'app': pod['app'],
            'cpu_cost_hour': cpu_cost_hour,
            'memory_cost_hour': memory_cost_hour,
            'total_cost_hour': total_cost_hour,
            'total_cost_month': total_cost_month,
            'cpu_request': pod['cpu_request'],
            'memory_request': pod['memory_request'],
            'cpu_usage': pod['cpu_usage'],
            'memory_usage': pod['memory_usage']
        }
        
        usage_stats[pod['name']] = {
            'cpu_usage_percent': cpu_usage_percent,
            'memory_usage_percent': memory_usage_percent,
            'cpu_waste': max(0, pod['cpu_request'] - pod['cpu_usage']),
            'memory_waste': max(0, pod['memory_request'] - pod['memory_usage'])
        }
    
    return costs, usage_stats

def generate_report(costs: Dict, usage_stats: Dict) -> str:
    """Générer un rapport de coûts"""
    report = []
    report.append("# 💰 RAPPORT D'ANALYSE FINOPS")
    report.append("=" * 50)
    report.append(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # Résumé par application
    app_totals = {}
    for pod_name, cost_data in costs.items():
        app = cost_data['app']
        if app not in app_totals:
            app_totals[app] = {
                'cost_hour': 0,
                'cost_month': 0,
                'cpu_total': 0,
                'memory_total': 0,
                'pods': 0
            }
        app_totals[app]['cost_hour'] += cost_data['total_cost_hour']
        app_totals[app]['cost_month'] += cost_data['total_cost_month']
        app_totals[app]['cpu_total'] += cost_data['cpu_request']
        app_totals[app]['memory_total'] += cost_data['memory_request']
        app_totals[app]['pods'] += 1
    
    report.append("## 📊 RÉSUMÉ PAR APPLICATION")
    report.append("-" * 30)
    report.append(f"{'App':<12} {'Pods':<5} {'CPU':<8} {'RAM':<8} {'€/heure':<10} {'€/mois':<10}")
    report.append("-" * 60)
    
    total_cost_hour = 0
    total_cost_month = 0
    
    for app, data in app_totals.items():
        total_cost_hour += data['cost_hour']
        total_cost_month += data['cost_month']
        report.append(f"{app:<12} {data['pods']:<5} {data['cpu_total']:<8.2f} {data['memory_total']:<8.2f} {data['cost_hour']:<10.4f} {data['cost_month']:<10.2f}")
    
    report.append("-" * 60)
    report.append(f"{'TOTAL':<12} {'':<5} {'':<8} {'':<8} {total_cost_hour:<10.4f} {total_cost_month:<10.2f}")
    report.append("")
    
    # Détail par pod
    report.append("## 🔍 DÉTAIL PAR POD")
    report.append("-" * 30)
    report.append(f"{'Pod':<25} {'CPU Req':<8} {'CPU Use':<8} {'RAM Req':<8} {'RAM Use':<8} {'€/mois':<10}")
    report.append("-" * 75)
    
    for pod_name, cost_data in costs.items():
        report.append(f"{pod_name:<25} {cost_data['cpu_request']:<8.3f} {cost_data['cpu_usage']:<8.3f} {cost_data['memory_request']:<8.2f} {cost_data['memory_usage']:<8.2f} {cost_data['total_cost_month']:<10.2f}")
    
    report.append("")
    
    # Analyse du gaspillage
    report.append("## ⚠️ ANALYSE DU GASPILLAGE")
    report.append("-" * 30)
    
    total_cpu_waste = 0
    total_memory_waste = 0
    
    for pod_name, stats in usage_stats.items():
        total_cpu_waste += stats['cpu_waste']
        total_memory_waste += stats['memory_waste']
        
        if stats['cpu_usage_percent'] < 50 or stats['memory_usage_percent'] < 50:
            report.append(f"🟡 {pod_name}:")
            report.append(f"   CPU: {stats['cpu_usage_percent']:.1f}% utilisé")
            report.append(f"   RAM: {stats['memory_usage_percent']:.1f}% utilisé")
    
    waste_cost_hour = (total_cpu_waste * PRICING['cpu_per_vcpu_hour']) + (total_memory_waste * PRICING['memory_per_gb_hour'])
    waste_cost_month = waste_cost_hour * PRICING['hours_per_month']
    
    report.append(f"\n💸 Gaspillage estimé: {waste_cost_month:.2f}€/mois ({waste_cost_month/total_cost_month*100:.1f}%)")
    
    # Recommandations
    report.append("\n## 💡 RECOMMANDATIONS")
    report.append("-" * 30)
    
    if waste_cost_month > total_cost_month * 0.2:
        report.append("🔴 Gaspillage élevé (>20%). Actions recommandées:")
        report.append("   - Réduire les requests CPU/RAM")
        report.append("   - Implémenter HPA (Horizontal Pod Autoscaler)")
        report.append("   - Considérer VPA (Vertical Pod Autoscaler)")
    elif waste_cost_month > total_cost_month * 0.1:
        report.append("🟡 Gaspillage modéré (10-20%). Actions suggérées:")
        report.append("   - Optimiser les requests sur les pods sous-utilisés")
        report.append("   - Surveiller les tendances d'utilisation")
    else:
        report.append("🟢 Utilisation optimale (<10% de gaspillage)")
    
    return "\n".join(report)

def main():
    """Fonction principale"""
    print("🔍 Analyse FinOps en cours...")
    
    # Récupérer les données des pods
    pods_data = get_pod_resources()
    if not pods_data:
        print("❌ Aucun pod trouvé dans le namespace finops-app")
        return
    
    # Calculer les coûts
    costs, usage_stats = calculate_costs(pods_data)
    
    # Générer le rapport
    report = generate_report(costs, usage_stats)
    
    # Afficher le rapport
    print(report)
    
    # Sauvegarder le rapport
    with open('../cost_analysis.md', 'w') as f:
        f.write(report)
    
    print(f"\n📄 Rapport sauvegardé dans: cost_analysis.md")

if __name__ == "__main__":
    main() 