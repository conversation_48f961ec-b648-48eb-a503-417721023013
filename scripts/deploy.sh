#!/bin/bash

# Script de déploiement FinOps Kubernetes
# Auteur: DevOps Team
# Date: $(date)

set -e

echo "🚀 Déploiement FinOps Kubernetes - Todo App"
echo "============================================="

# Variables
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
NAMESPACE="finops-app"

# Fonction pour afficher le statut
show_status() {
    echo "📊 Statut des pods:"
    kubectl get pods -n $NAMESPACE -o wide
    echo ""
    echo "🔗 Services:"
    kubectl get svc -n $NAMESPACE
    echo ""
}

# Fonction pour attendre que les pods soient prêts
wait_for_pods() {
    local app_name=$1
    echo "⏳ Attente que les pods $app_name soient prêts..."
    kubectl wait --for=condition=Ready pod -l app=$app_name -n $NAMESPACE --timeout=300s
}

# Étape 1: Démarrer Minikube
echo "🔧 Démarrage de Minikube..."
minikube start --cpus=4 --memory=6000 --driver=docker

# Étape 2: Configurer l'environnement Docker
echo "🐳 Configuration de l'environnement Docker..."
eval $(minikube docker-env)

# Étape 3: Build des images
echo "📦 Build des images Docker..."
cd $PROJECT_DIR
docker build -t frontend:latest ./frontend
docker build -t backend:latest ./backend

# Étape 4: Créer le namespace
echo "🏗️ Création du namespace..."
kubectl apply -f k8s/namespace.yaml

# Étape 5: Déployer les ConfigMaps et Secrets
echo "⚙️ Déploiement des ConfigMaps et Secrets..."
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secret.yaml

# Étape 6: Déployer le PVC
echo "💾 Déploiement du stockage persistant..."
kubectl apply -f k8s/pvc.yaml

# Étape 7: Déployer MongoDB
echo "🗄️ Déploiement de MongoDB..."
kubectl apply -f k8s/mongo-deployment.yaml
wait_for_pods "mongo"

# Étape 8: Déployer le backend
echo "🔧 Déploiement du backend..."
kubectl apply -f k8s/backend-deployment.yaml
wait_for_pods "backend"

# Étape 9: Déployer le frontend
echo "🎨 Déploiement du frontend..."
kubectl apply -f k8s/frontend-deployment.yaml
wait_for_pods "frontend"

# Étape 10: Déployer les services
echo "🌐 Déploiement des services..."
kubectl apply -f k8s/services.yaml

# Étape 11: Afficher le statut final
echo "✅ Déploiement terminé!"
show_status

# Étape 12: Obtenir l'URL d'accès
echo "🌍 URL d'accès à l'application:"
minikube service frontend-service -n $NAMESPACE --url

echo ""
echo "🎉 Application déployée avec succès!"
echo "📋 Commandes utiles:"
echo "  - Logs backend: kubectl logs -f deployment/backend -n $NAMESPACE"
echo "  - Logs frontend: kubectl logs -f deployment/frontend -n $NAMESPACE"
echo "  - Statut: kubectl get pods -n $NAMESPACE"
echo "  - Accès app: minikube service frontend-service -n $NAMESPACE" 