#!/usr/bin/env python3

"""
Comparateur de coûts FinOps - Avant/Après Optimisation
Auteur: DevOps Team
Date: 2024
"""

from datetime import datetime

# Prix simulés (en euros)
PRICING = {
    'cpu_per_vcpu_hour': 0.02,
    'memory_per_gb_hour': 0.01,
    'hours_per_month': 720
}

def parse_memory(memory_str: str) -> float:
    """Convertir une valeur mémoire en GB"""
    if memory_str.endswith('Mi'):
        return float(memory_str[:-2]) / 1024
    elif memory_str.endswith('Gi'):
        return float(memory_str[:-2])
    return 0.0

def calculate_component_cost(cpu_vcpu: float, memory_gb: float, replicas: int) -> dict:
    """Calculer le coût d'un composant"""
    cpu_cost_hour = cpu_vcpu * PRICING['cpu_per_vcpu_hour'] * replicas
    memory_cost_hour = memory_gb * PRICING['memory_per_gb_hour'] * replicas
    total_cost_hour = cpu_cost_hour + memory_cost_hour
    total_cost_month = total_cost_hour * PRICING['hours_per_month']
    
    return {
        'cpu_cost_hour': cpu_cost_hour,
        'memory_cost_hour': memory_cost_hour,
        'total_cost_hour': total_cost_hour,
        'total_cost_month': total_cost_month,
        'cpu_vcpu': cpu_vcpu,
        'memory_gb': memory_gb,
        'replicas': replicas
    }

def main():
    print("🔍 COMPARAISON COÛTS FINOPS - AVANT/APRÈS OPTIMISATION")
    print("=" * 60)
    
    # Configuration AVANT optimisation
    print("\n📊 CONFIGURATION INITIALE")
    print("-" * 30)
    
    original_config = {
        'frontend': calculate_component_cost(0.25, parse_memory("256Mi"), 2),
        'backend': calculate_component_cost(0.5, parse_memory("512Mi"), 2),
        'mongo': calculate_component_cost(0.5, parse_memory("512Mi"), 1)
    }
    
    print(f"{'Composant':<12} {'Replicas':<8} {'CPU':<8} {'RAM':<8} {'€/mois':<10}")
    print("-" * 60)
    
    total_original = 0
    for name, config in original_config.items():
        total_original += config['total_cost_month']
        print(f"{name.capitalize():<12} {config['replicas']:<8} {config['cpu_vcpu']:<8.2f} {config['memory_gb']:<8.2f} {config['total_cost_month']:<10.2f}")
    
    print("-" * 60)
    print(f"{'TOTAL ORIGINAL':<37} {total_original:<10.2f}")
    
    # Configuration APRÈS optimisation
    print("\n🚀 CONFIGURATION OPTIMISÉE")
    print("-" * 30)
    
    optimized_config = {
        'frontend': calculate_component_cost(0.1, parse_memory("128Mi"), 1),
        'backend': calculate_component_cost(0.25, parse_memory("256Mi"), 1),
        'mongo': calculate_component_cost(0.25, parse_memory("256Mi"), 1)
    }
    
    print(f"{'Composant':<12} {'Replicas':<8} {'CPU':<8} {'RAM':<8} {'€/mois':<10}")
    print("-" * 60)
    
    total_optimized = 0
    for name, config in optimized_config.items():
        total_optimized += config['total_cost_month']
        print(f"{name.capitalize():<12} {config['replicas']:<8} {config['cpu_vcpu']:<8.2f} {config['memory_gb']:<8.2f} {config['total_cost_month']:<10.2f}")
    
    print("-" * 60)
    print(f"{'TOTAL OPTIMISÉ':<37} {total_optimized:<10.2f}")
    
    # Calcul des économies
    print("\n💰 ÉCONOMIES RÉALISÉES")
    print("-" * 30)
    
    savings_monthly = total_original - total_optimized
    savings_percentage = (savings_monthly / total_original) * 100
    savings_yearly = savings_monthly * 12
    
    print(f"Coût original:      {total_original:.2f} €/mois")
    print(f"Coût optimisé:      {total_optimized:.2f} €/mois")
    print(f"Économies:          {savings_monthly:.2f} €/mois ({savings_percentage:.1f}%)")
    print(f"Économies annuelles: {savings_yearly:.2f} €/an")
    
    # Détail des optimisations
    print("\n🔧 DÉTAIL DES OPTIMISATIONS")
    print("-" * 30)
    
    for component in ['frontend', 'backend', 'mongo']:
        original = original_config[component]
        optimized = optimized_config[component]
        
        cpu_reduction = ((original['cpu_vcpu'] - optimized['cpu_vcpu']) / original['cpu_vcpu']) * 100
        memory_reduction = ((original['memory_gb'] - optimized['memory_gb']) / original['memory_gb']) * 100
        replica_reduction = ((original['replicas'] - optimized['replicas']) / original['replicas']) * 100 if original['replicas'] > 0 else 0
        cost_reduction = ((original['total_cost_month'] - optimized['total_cost_month']) / original['total_cost_month']) * 100
        
        print(f"\n{component.upper()}:")
        print(f"  CPU: {original['cpu_vcpu']:.2f} → {optimized['cpu_vcpu']:.2f} vCPU (-{cpu_reduction:.1f}%)")
        print(f"  RAM: {original['memory_gb']:.2f} → {optimized['memory_gb']:.2f} GB (-{memory_reduction:.1f}%)")
        print(f"  Replicas: {original['replicas']} → {optimized['replicas']} (-{replica_reduction:.1f}%)")
        print(f"  Coût: {original['total_cost_month']:.2f} → {optimized['total_cost_month']:.2f} €/mois (-{cost_reduction:.1f}%)")
    
    # ROI et recommandations
    print("\n📈 RETOUR SUR INVESTISSEMENT")
    print("-" * 30)
    
    development_hours = 8
    hourly_rate = 50  # €/heure
    development_cost = development_hours * hourly_rate
    
    months_to_roi = development_cost / savings_monthly if savings_monthly > 0 else float('inf')
    
    print(f"Coût développement: {development_cost:.2f} €")
    print(f"Économies mensuelles: {savings_monthly:.2f} €")
    print(f"ROI atteint en: {months_to_roi:.1f} mois")
    
    # Statut et recommandations
    print("\n🎯 STATUT ET RECOMMANDATIONS")
    print("-" * 30)
    
    if savings_percentage > 50:
        print("🟢 EXCELLENT: Optimisation très réussie (>50%)")
        print("   → Appliquer cette méthodologie à d'autres projets")
    elif savings_percentage > 20:
        print("🟡 BON: Optimisation réussie (20-50%)")
        print("   → Chercher des optimisations supplémentaires")
    else:
        print("🔴 FAIBLE: Optimisation limitée (<20%)")
        print("   → Revoir la stratégie d'optimisation")
    
    print("\nPROCHAINES ÉTAPES:")
    print("1. Implémenter HPA (Horizontal Pod Autoscaler)")
    print("2. Configurer VPA (Vertical Pod Autoscaler)")
    print("3. Surveiller l'utilisation réelle")
    print("4. Ajuster les requests selon les métriques")
    
    # Sauvegarde du rapport
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"cost_comparison_{timestamp}.md"
    
    with open(filename, 'w') as f:
        f.write(f"# Comparaison Coûts FinOps\n\n")
        f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"## Résumé\n")
        f.write(f"- Coût original: {total_original:.2f} €/mois\n")
        f.write(f"- Coût optimisé: {total_optimized:.2f} €/mois\n")
        f.write(f"- Économies: {savings_monthly:.2f} €/mois ({savings_percentage:.1f}%)\n")
        f.write(f"- ROI: {months_to_roi:.1f} mois\n")
    
    print(f"\n📄 Rapport détaillé sauvegardé: {filename}")

if __name__ == "__main__":
    main() 