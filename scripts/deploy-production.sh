#!/bin/bash

# FinOps Production Deployment Script
# ===================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NAMESPACE="finops-production"
DOCKER_REGISTRY="ghcr.io"
IMAGE_TAG="${1:-latest}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "docker is not installed"
        exit 1
    fi
    
    # Check if helm is installed
    if ! command -v helm &> /dev/null; then
        log_warning "helm is not installed (optional)"
    fi
    
    # Check kubectl connection
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Create namespace if it doesn't exist
create_namespace() {
    log_info "Creating namespace if it doesn't exist..."
    
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_info "Namespace $NAMESPACE already exists"
    else
        kubectl apply -f "$PROJECT_ROOT/k8s/production/namespace.yaml"
        log_success "Namespace $NAMESPACE created"
    fi
}

# Build and push Docker images
build_and_push_images() {
    log_info "Building and pushing Docker images..."
    
    # Build backend image
    log_info "Building backend image..."
    docker build -t "$DOCKER_REGISTRY/finops/backend:$IMAGE_TAG" \
        --target production \
        "$PROJECT_ROOT/backend"
    
    # Build frontend image
    log_info "Building frontend image..."
    docker build -t "$DOCKER_REGISTRY/finops/frontend:$IMAGE_TAG" \
        --target production \
        -f "$PROJECT_ROOT/frontend/Dockerfile.production" \
        --build-arg REACT_APP_API_URL="https://api.your-domain.com/api" \
        --build-arg REACT_APP_ENV="production" \
        "$PROJECT_ROOT/frontend"
    
    # Push images
    log_info "Pushing images to registry..."
    docker push "$DOCKER_REGISTRY/finops/backend:$IMAGE_TAG"
    docker push "$DOCKER_REGISTRY/finops/frontend:$IMAGE_TAG"
    
    log_success "Images built and pushed successfully"
}

# Deploy MongoDB
deploy_mongodb() {
    log_info "Deploying MongoDB..."
    
    kubectl apply -f "$PROJECT_ROOT/k8s/production/mongodb.yaml"
    
    # Wait for MongoDB to be ready
    log_info "Waiting for MongoDB to be ready..."
    kubectl wait --for=condition=ready pod -l app=mongodb -n "$NAMESPACE" --timeout=300s
    
    log_success "MongoDB deployed successfully"
}

# Deploy backend
deploy_backend() {
    log_info "Deploying backend..."
    
    # Update image tag in deployment
    sed "s|image: finops/backend:latest|image: $DOCKER_REGISTRY/finops/backend:$IMAGE_TAG|g" \
        "$PROJECT_ROOT/k8s/production/backend.yaml" | kubectl apply -f -
    
    # Wait for deployment to be ready
    log_info "Waiting for backend deployment to be ready..."
    kubectl rollout status deployment/backend -n "$NAMESPACE" --timeout=300s
    
    log_success "Backend deployed successfully"
}

# Deploy frontend
deploy_frontend() {
    log_info "Deploying frontend..."
    
    # Update image tag in deployment
    sed "s|image: finops/frontend:latest|image: $DOCKER_REGISTRY/finops/frontend:$IMAGE_TAG|g" \
        "$PROJECT_ROOT/k8s/production/frontend.yaml" | kubectl apply -f -
    
    # Wait for deployment to be ready
    log_info "Waiting for frontend deployment to be ready..."
    kubectl rollout status deployment/frontend -n "$NAMESPACE" --timeout=300s
    
    log_success "Frontend deployed successfully"
}

# Deploy monitoring
deploy_monitoring() {
    log_info "Deploying monitoring stack..."
    
    # Create monitoring namespace
    kubectl create namespace monitoring --dry-run=client -o yaml | kubectl apply -f -
    
    # Deploy Prometheus
    kubectl apply -f "$PROJECT_ROOT/k8s/monitoring/"
    
    log_success "Monitoring stack deployed successfully"
}

# Run health checks
run_health_checks() {
    log_info "Running health checks..."
    
    # Check backend health
    log_info "Checking backend health..."
    kubectl exec -n "$NAMESPACE" deployment/backend -- curl -f http://localhost:8080/health
    
    # Check if all pods are running
    log_info "Checking pod status..."
    kubectl get pods -n "$NAMESPACE"
    
    # Check services
    log_info "Checking services..."
    kubectl get services -n "$NAMESPACE"
    
    log_success "Health checks passed"
}

# Run smoke tests
run_smoke_tests() {
    log_info "Running smoke tests..."
    
    # Port forward to backend for testing
    kubectl port-forward svc/backend 8080:8080 -n "$NAMESPACE" &
    PORT_FORWARD_PID=$!
    
    sleep 5
    
    # Test API endpoints
    if curl -f http://localhost:8080/health; then
        log_success "Backend health check passed"
    else
        log_error "Backend health check failed"
        kill $PORT_FORWARD_PID
        exit 1
    fi
    
    # Test authentication endpoint
    if curl -f -X POST http://localhost:8080/api/auth/login \
        -H "Content-Type: application/json" \
        -d '{"identifier":"test","password":"test"}' | grep -q "success"; then
        log_info "Authentication endpoint responding"
    else
        log_info "Authentication endpoint responding with expected error"
    fi
    
    # Clean up port forward
    kill $PORT_FORWARD_PID
    
    log_success "Smoke tests completed"
}

# Backup current deployment
backup_deployment() {
    log_info "Creating backup of current deployment..."
    
    BACKUP_DIR="$PROJECT_ROOT/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup current deployments
    kubectl get deployment backend -n "$NAMESPACE" -o yaml > "$BACKUP_DIR/backend-deployment.yaml"
    kubectl get deployment frontend -n "$NAMESPACE" -o yaml > "$BACKUP_DIR/frontend-deployment.yaml"
    
    # Backup configmaps and secrets
    kubectl get configmap -n "$NAMESPACE" -o yaml > "$BACKUP_DIR/configmaps.yaml"
    kubectl get secret -n "$NAMESPACE" -o yaml > "$BACKUP_DIR/secrets.yaml"
    
    log_success "Backup created at $BACKUP_DIR"
}

# Rollback function
rollback_deployment() {
    log_warning "Rolling back deployment..."
    
    kubectl rollout undo deployment/backend -n "$NAMESPACE"
    kubectl rollout undo deployment/frontend -n "$NAMESPACE"
    
    # Wait for rollback to complete
    kubectl rollout status deployment/backend -n "$NAMESPACE"
    kubectl rollout status deployment/frontend -n "$NAMESPACE"
    
    log_success "Rollback completed"
}

# Main deployment function
main() {
    log_info "Starting FinOps production deployment..."
    log_info "Image tag: $IMAGE_TAG"
    log_info "Namespace: $NAMESPACE"
    
    # Trap to handle errors
    trap 'log_error "Deployment failed! Check the logs above."; exit 1' ERR
    
    check_prerequisites
    create_namespace
    backup_deployment
    
    # Build and push images (skip if using existing images)
    if [[ "${SKIP_BUILD:-false}" != "true" ]]; then
        build_and_push_images
    else
        log_info "Skipping image build (SKIP_BUILD=true)"
    fi
    
    deploy_mongodb
    deploy_backend
    deploy_frontend
    
    # Deploy monitoring (optional)
    if [[ "${DEPLOY_MONITORING:-true}" == "true" ]]; then
        deploy_monitoring
    else
        log_info "Skipping monitoring deployment (DEPLOY_MONITORING=false)"
    fi
    
    run_health_checks
    run_smoke_tests
    
    log_success "🚀 FinOps production deployment completed successfully!"
    log_info "Access the application at: https://your-domain.com"
    log_info "Monitor the application at: https://monitoring.your-domain.com"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        rollback_deployment
        ;;
    "health")
        run_health_checks
        ;;
    "smoke")
        run_smoke_tests
        ;;
    *)
        echo "Usage: $0 [deploy|rollback|health|smoke] [image-tag]"
        echo "  deploy   - Deploy the application (default)"
        echo "  rollback - Rollback to previous version"
        echo "  health   - Run health checks"
        echo "  smoke    - Run smoke tests"
        exit 1
        ;;
esac
