#!/bin/bash

# Comprehensive deployment script with validation
# Usage: ./scripts/deploy-with-validation.sh [--skip-tests] [--cleanup]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="finops-app"
FRONTEND_SERVICE="frontend-service"
BACKEND_SERVICE="backend-service"
MONGO_SERVICE="mongo-service"
TIMEOUT=300

# Flags
SKIP_TESTS=false
CLEANUP=false

# Parse arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --skip-tests)
      SKIP_TESTS=true
      shift
      ;;
    --cleanup)
      CLEANUP=true
      shift
      ;;
    *)
      echo "Unknown option $1"
      exit 1
      ;;
  esac
done

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

check_requirements() {
    log "Checking requirements..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check if minikube is installed
    if ! command -v minikube &> /dev/null; then
        error "minikube is not installed or not in PATH"
        exit 1
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        error "docker is not installed or not in PATH"
        exit 1
    fi
    
    success "All requirements met"
}

start_minikube() {
    log "Starting Minikube..."
    
    if ! minikube status &> /dev/null; then
        minikube start --cpus=4 --memory=6000 --driver=docker
        success "Minikube started"
    else
        success "Minikube already running"
    fi
}

setup_docker_env() {
    log "Setting up Docker environment..."
    eval $(minikube docker-env)
    success "Docker environment configured for Minikube"
}

build_images() {
    log "Building Docker images..."
    
    # Build frontend
    log "Building frontend image..."
    docker build -t frontend:latest ./frontend
    success "Frontend image built"
    
    # Build backend
    log "Building backend image..."
    docker build -t backend:latest ./backend
    success "Backend image built"
}

deploy_kubernetes() {
    log "Deploying to Kubernetes..."
    
    # Create namespace
    kubectl apply -f k8s/namespace.yaml
    
    # Deploy configs and secrets
    kubectl apply -f k8s/configmap.yaml
    kubectl apply -f k8s/secret.yaml
    kubectl apply -f k8s/pvc.yaml
    
    # Deploy services
    kubectl apply -f k8s/mongo-deployment.yaml
    kubectl apply -f k8s/backend-deployment.yaml
    kubectl apply -f k8s/frontend-deployment.yaml
    kubectl apply -f k8s/services.yaml
    
    success "Kubernetes manifests applied"
}

wait_for_pods() {
    log "Waiting for pods to be ready..."
    
    # Wait for MongoDB
    kubectl wait --for=condition=ready pod -l app=mongo -n $NAMESPACE --timeout=${TIMEOUT}s
    success "MongoDB pod ready"
    
    # Wait for Backend
    kubectl wait --for=condition=ready pod -l app=backend -n $NAMESPACE --timeout=${TIMEOUT}s
    success "Backend pod ready"
    
    # Wait for Frontend
    kubectl wait --for=condition=ready pod -l app=frontend -n $NAMESPACE --timeout=${TIMEOUT}s
    success "Frontend pod ready"
}

validate_deployment() {
    log "Validating deployment..."
    
    # Check pod status
    log "Checking pod status..."
    kubectl get pods -n $NAMESPACE
    
    # Check services
    log "Checking services..."
    kubectl get services -n $NAMESPACE
    
    # Test backend health
    log "Testing backend health..."
    BACKEND_URL=$(minikube service $BACKEND_SERVICE -n $NAMESPACE --url)
    if curl -f "$BACKEND_URL/health" &> /dev/null; then
        success "Backend health check passed"
    else
        error "Backend health check failed"
        return 1
    fi
    
    # Test frontend health
    log "Testing frontend health..."
    FRONTEND_URL=$(minikube service $FRONTEND_SERVICE -n $NAMESPACE --url)
    if curl -f "$FRONTEND_URL/health" &> /dev/null; then
        success "Frontend health check passed"
    else
        error "Frontend health check failed"
        return 1
    fi
    
    # Test API connectivity
    log "Testing API connectivity..."
    if curl -f "$BACKEND_URL/api/todos" &> /dev/null; then
        success "API connectivity test passed"
    else
        error "API connectivity test failed"
        return 1
    fi
    
    success "All validation checks passed"
}

run_tests() {
    if [ "$SKIP_TESTS" = true ]; then
        warning "Skipping tests as requested"
        return 0
    fi
    
    log "Installing test dependencies..."
    npm install @playwright/test
    npx playwright install
    
    log "Running E2E tests..."
    # Set test environment variables
    export FRONTEND_URL=$(minikube service $FRONTEND_SERVICE -n $NAMESPACE --url)
    export BACKEND_URL=$(minikube service $BACKEND_SERVICE -n $NAMESPACE --url)
    
    # Run tests
    if npm test; then
        success "All tests passed"
    else
        error "Some tests failed"
        return 1
    fi
}

show_access_info() {
    log "Deployment complete! Access information:"
    
    FRONTEND_URL=$(minikube service $FRONTEND_SERVICE -n $NAMESPACE --url)
    BACKEND_URL=$(minikube service $BACKEND_SERVICE -n $NAMESPACE --url)
    
    echo ""
    echo "🌐 Frontend URL: $FRONTEND_URL"
    echo "🔗 Backend URL: $BACKEND_URL"
    echo "📊 API Docs: $BACKEND_URL/api/todos"
    echo "💚 Health Check: $BACKEND_URL/health"
    echo ""
    echo "📱 To open in browser:"
    echo "   minikube service $FRONTEND_SERVICE -n $NAMESPACE"
    echo ""
    echo "🔍 Monitor resources:"
    echo "   kubectl get all -n $NAMESPACE"
    echo "   kubectl top pods -n $NAMESPACE"
    echo ""
}

cleanup_deployment() {
    if [ "$CLEANUP" = true ]; then
        log "Cleaning up deployment..."
        kubectl delete namespace $NAMESPACE --ignore-not-found=true
        success "Cleanup completed"
    fi
}

main() {
    log "Starting deployment process..."
    
    if [ "$CLEANUP" = true ]; then
        cleanup_deployment
        return 0
    fi
    
    check_requirements
    start_minikube
    setup_docker_env
    build_images
    deploy_kubernetes
    wait_for_pods
    validate_deployment
    run_tests
    show_access_info
    
    success "🎉 Deployment completed successfully!"
}

# Trap errors and cleanup
trap 'error "Deployment failed at line $LINENO"' ERR

main "$@"