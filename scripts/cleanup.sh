#!/bin/bash

# Script de nettoyage FinOps Kubernetes
# Auteur: DevOps Team
# Date: $(date)

set -e

echo "🧹 Nettoyage FinOps Kubernetes - Todo App"
echo "========================================="

NAMESPACE="finops-app"

# Fonction pour confirmer l'action
confirm_action() {
    read -p "Êtes-vous sûr de vouloir supprimer tous les composants? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Annulation du nettoyage"
        exit 1
    fi
}

# Demander confirmation
confirm_action

echo "🗑️ Suppression des ressources Kubernetes..."

# Supprimer les services
echo "🌐 Suppression des services..."
kubectl delete -f k8s/services.yaml --ignore-not-found

# Supprimer les déploiements
echo "🔧 Suppression des déploiements..."
kubectl delete -f k8s/frontend-deployment.yaml --ignore-not-found
kubectl delete -f k8s/backend-deployment.yaml --ignore-not-found
kubectl delete -f k8s/mongo-deployment.yaml --ignore-not-found

# Supprimer le PVC
echo "💾 Suppression du stockage persistant..."
kubectl delete -f k8s/pvc.yaml --ignore-not-found

# Supprimer les ConfigMaps et Secrets
echo "⚙️ Suppression des ConfigMaps et Secrets..."
kubectl delete -f k8s/configmap.yaml --ignore-not-found
kubectl delete -f k8s/secret.yaml --ignore-not-found

# Supprimer le namespace
echo "🏗️ Suppression du namespace..."
kubectl delete -f k8s/namespace.yaml --ignore-not-found

# Nettoyer les images Docker
echo "🐳 Nettoyage des images Docker..."
eval $(minikube docker-env)
docker rmi frontend:latest backend:latest --force 2>/dev/null || true

echo "✅ Nettoyage terminé!"
echo "📋 Vérification:"
kubectl get pods -n $NAMESPACE 2>/dev/null || echo "Namespace supprimé avec succès" 