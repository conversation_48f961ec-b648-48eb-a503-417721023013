# 🚀 FinOps Production Deployment Guide

## 📋 **Overview**

This guide provides comprehensive instructions for deploying the FinOps application to production environments using Kubernetes, Docker, and modern DevOps practices.

## 🏗️ **Architecture Overview**

### **Production Stack:**
- **Frontend:** React application served by <PERSON>inx
- **Backend:** Node.js API with Express framework
- **Database:** MongoDB with replica set
- **Cache:** Redis for session storage
- **Monitoring:** Prometheus + Grafana
- **Load Balancer:** Nginx with SSL termination
- **Container Orchestration:** Kubernetes
- **CI/CD:** GitHub Actions

### **Security Features:**
- JWT-based authentication
- Rate limiting and DDoS protection
- SSL/TLS encryption
- Security headers (Helmet.js)
- Input sanitization and validation
- Role-based access control (RBAC)

## 🔧 **Prerequisites**

### **Required Tools:**
- Docker 20.10+
- Kubernetes 1.24+
- kubectl configured for your cluster
- Helm 3.0+ (optional)
- Git

### **Required Secrets:**
- JWT secret key
- MongoDB credentials
- SSL certificates
- Container registry credentials
- Monitoring credentials

## 📦 **Deployment Options**

### **Option 1: Kubernetes Deployment (Recommended)**

#### **1. Prepare Environment**
```bash
# Clone the repository
git clone https://github.com/Anderson-Archimede/Ecommerce-FINOPS.git
cd Ecommerce-FINOPS

# Set environment variables
export NAMESPACE=finops-production
export IMAGE_TAG=v1.0.0
export DOMAIN=your-domain.com
```

#### **2. Create Secrets**
```bash
# Create namespace
kubectl apply -f k8s/production/namespace.yaml

# Create secrets
kubectl create secret generic backend-secret \
  --from-literal=jwt-secret="your-super-secure-jwt-secret" \
  --from-literal=db-url="*************************************************************************" \
  -n finops-production

kubectl create secret tls tls-secret \
  --cert=path/to/cert.pem \
  --key=path/to/key.pem \
  -n finops-production
```

#### **3. Deploy Application**
```bash
# Make deployment script executable
chmod +x scripts/deploy-production.sh

# Deploy to production
./scripts/deploy-production.sh v1.0.0
```

### **Option 2: Docker Compose Deployment**

#### **1. Configure Environment**
```bash
# Copy production environment file
cp .env.production .env

# Edit environment variables
nano .env
```

#### **2. Deploy with Docker Compose**
```bash
# Deploy production stack
docker-compose -f docker-compose.production.yml up -d

# Check status
docker-compose -f docker-compose.production.yml ps
```

## 🔐 **Security Configuration**

### **SSL/TLS Setup**
```bash
# Generate SSL certificates (if not using Let's Encrypt)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout certs/key.pem \
  -out certs/cert.pem \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=your-domain.com"
```

### **Environment Variables**
```bash
# Required production environment variables
NODE_ENV=production
JWT_SECRET=your-super-secure-jwt-secret-change-in-production
DB_URL=mongodb://mongodb:27017/finops_production
CORS_ORIGIN=https://your-domain.com,https://www.your-domain.com
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AUTH_MAX_REQUESTS=5
```

## 📊 **Monitoring Setup**

### **Prometheus Configuration**
```bash
# Deploy monitoring stack
kubectl apply -f k8s/monitoring/

# Access Prometheus
kubectl port-forward svc/prometheus 9090:9090 -n monitoring
```

### **Grafana Dashboard**
```bash
# Access Grafana
kubectl port-forward svc/grafana 3000:3000 -n monitoring

# Default credentials: admin/admin
```

### **Key Metrics to Monitor**
- **Application Metrics:**
  - Request rate and response time
  - Error rate and status codes
  - Memory and CPU usage
  - Database connection pool

- **Business Metrics:**
  - Total cost tracking
  - Budget utilization
  - Cost optimization opportunities
  - User activity and authentication

- **Infrastructure Metrics:**
  - Pod health and restarts
  - Node resource usage
  - Network traffic
  - Storage utilization

## 🔄 **CI/CD Pipeline**

### **GitHub Actions Workflow**
The production deployment is automated using GitHub Actions:

1. **Test Phase:**
   - Unit tests (backend/frontend)
   - Integration tests
   - Security scans
   - Playwright E2E tests

2. **Build Phase:**
   - Docker image builds
   - Multi-stage optimization
   - Security scanning
   - Registry push

3. **Deploy Phase:**
   - Staging deployment
   - Smoke tests
   - Production deployment
   - Health checks

### **Manual Deployment**
```bash
# Deploy specific version
./scripts/deploy-production.sh v1.2.0

# Rollback if needed
./scripts/deploy-production.sh rollback

# Run health checks
./scripts/deploy-production.sh health
```

## 🚨 **Troubleshooting**

### **Common Issues**

#### **Pod Not Starting**
```bash
# Check pod status
kubectl get pods -n finops-production

# Check pod logs
kubectl logs deployment/backend -n finops-production

# Describe pod for events
kubectl describe pod <pod-name> -n finops-production
```

#### **Database Connection Issues**
```bash
# Check MongoDB status
kubectl exec -it deployment/mongodb -n finops-production -- mongo --eval "db.adminCommand('ping')"

# Check connection string
kubectl get secret backend-secret -n finops-production -o yaml
```

#### **SSL Certificate Issues**
```bash
# Check certificate validity
openssl x509 -in certs/cert.pem -text -noout

# Verify certificate in cluster
kubectl get secret tls-secret -n finops-production -o yaml
```

### **Performance Issues**
```bash
# Check resource usage
kubectl top pods -n finops-production

# Scale deployment if needed
kubectl scale deployment backend --replicas=5 -n finops-production

# Check HPA status
kubectl get hpa -n finops-production
```

## 📈 **Scaling and Optimization**

### **Horizontal Pod Autoscaling**
```yaml
# HPA is configured to scale based on:
# - CPU utilization (70%)
# - Memory utilization (80%)
# - Custom metrics (request rate)
```

### **Database Optimization**
- Connection pooling configured
- Indexes optimized for FinOps queries
- Read replicas for analytics workloads

### **Caching Strategy**
- Redis for session storage
- Application-level caching for cost data
- CDN for static assets

## 🔒 **Backup and Recovery**

### **Database Backup**
```bash
# Automated daily backups configured
# Manual backup
kubectl exec deployment/mongodb -n finops-production -- mongodump --out /backup/$(date +%Y%m%d)
```

### **Application Backup**
```bash
# Backup current deployment
./scripts/deploy-production.sh backup

# Restore from backup
kubectl apply -f backups/20250714_120000/
```

## 📞 **Support and Maintenance**

### **Health Checks**
- **Application:** `/health` and `/ready` endpoints
- **Database:** Connection and query tests
- **External Services:** API connectivity checks

### **Log Management**
```bash
# View application logs
kubectl logs -f deployment/backend -n finops-production

# View aggregated logs
kubectl logs -l app=backend -n finops-production --tail=100
```

### **Maintenance Windows**
- **Recommended:** Sunday 2:00-4:00 AM UTC
- **Process:** Rolling updates with zero downtime
- **Rollback:** Automated rollback on failure

## 🎯 **Performance Benchmarks**

### **Expected Performance:**
- **Response Time:** <2s for dashboard
- **Throughput:** 1000+ requests/minute
- **Availability:** 99.9% uptime
- **Recovery Time:** <5 minutes

### **Resource Requirements:**
- **CPU:** 2-4 cores per service
- **Memory:** 1-2GB per service
- **Storage:** 50GB+ for database
- **Network:** 1Gbps recommended

## 📚 **Additional Resources**

- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)
- [Prometheus Monitoring](https://prometheus.io/docs/)
- [Security Hardening Guide](./SECURITY.md)

---

**Last Updated:** July 14, 2025  
**Version:** 1.0.0  
**Maintainer:** FinOps Team
